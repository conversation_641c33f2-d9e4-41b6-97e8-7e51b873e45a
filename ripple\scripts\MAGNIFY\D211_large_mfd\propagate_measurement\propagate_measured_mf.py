from ripple.field import Trans<PERSON><PERSON><PERSON>
from ripple.monitors import FieldMonitor
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.sim_helpers import run_sim
from ripple.field_distribution import ImportedImageFieldDistribution

from ripple.utils.export_data import plot_field_grid_far_field
import matplotlib.pyplot as plt

# Imported source from MFD measurement
source_field = ImportedImageFieldDistribution(r'0_0.0.tiff', pixel_size_um=0.15)
source_field = TransverseField(distribution=source_field, z0=0, refractive_index=1., wavelength=1.55)

monitor_xy = FieldMonitor(monitor_type='xz', position=10, saving_path=None, record_beam_radius_per_z=True)

scene = CouplingScene(input_field=source_field, target_field=None,
                      background_material_index=1.,
                      optical_structures=[],
                      sim_size_xy=[100, 100], sim_z_end=20,
                      wavelength_sampling_xy=8,
                      material_wavelength_sampling_z=8,
                      background_wavelength_sampling_z=4,
                      monitors=[monitor_xy],
                      solver='wpm',
                      boundary_condition='ABC', boundary_parameters=None)

source_field.view()
plt.show()

run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=False,
        name='optimized', sim_result_dir=None, opt_log_dir=None, show_plot=True)

