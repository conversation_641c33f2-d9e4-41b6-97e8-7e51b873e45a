import sys
sys.path.append('../')
from AQ6317B_wrapper import ANDO_AQ6317B


AQ6317B_OSA_gpib_address = 16


def init_osa(sensitivity='NORM RANGE HOLD', sampling_points=2001, display_trace='A', ref_level_dbm=None,
             wl_start=None, wl_stop=None, resolution=None):
    assert sensitivity in ['NORM RANGE HOLD', 'NORM RANGE AUTO', 'MID', 'HIGH1', 'HIGH2', 'HIGH3']
    assert display_trace.upper() in ['A', 'B', 'C'], "display_trace can only be 'A', 'B' or 'C'"

    try:
        osa = ANDO_AQ6317B(u'GPIB0::%i::INSTR' % AQ6317B_OSA_gpib_address)
        # osa.initialize_hardware()

        osa.set_sampling_point(nb_points=sampling_points)
        osa.set_sensitivity(sensitivity)
        osa.set_power_unit('dBm')

        for tr in ['A', 'B', 'C']:
            if tr == display_trace:
                print("display {}".format(tr))
                osa.display_trace(tr, True)
                osa.write_trace(tr)
            else:
                print("hide {}".format(tr))
                osa.fix_trace(tr)
                osa.display_trace(tr, False)

        osa.auto_offset(False)
        osa.wl_shift(0)

        if not ref_level_dbm:
            assert isinstance(ref_level_dbm, (int, float))
            osa.set_ref_level_dbm(ref_level_dbm)

        if wl_start and wl_stop is not None:
            osa.set_wl_range(wl_start=wl_start, wl_stop=wl_stop)
        if resolution:
            osa.set_wl_resolution(resolution)

        print("- OSA {} is initialized.\n".format(osa))
        osa.single_sweep()
        return osa
    except IOError:
        print("Please turn on the OSA!")
        raise


if __name__ == "__main__":
    osa = init_osa(sensitivity='HIGH1', sampling_points=2001, display_trace='A', ref_level_dbm=-80,
                   wl_start=1530, wl_stop=1570, resolution=1)
