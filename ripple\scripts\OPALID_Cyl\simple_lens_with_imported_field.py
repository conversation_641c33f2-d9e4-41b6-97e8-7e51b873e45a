import numpy as np
import os
import matplotlib.pyplot as plt

from ripple.field import Transverse<PERSON>ield
from ripple.field_distribution import GaussianDistribution, ImportedFieldDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens, SingleSurface, ImportedModel
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_file
from ripple.functionalities.far_field import plot_far_field_from_monitor
from ripple.utils.export_data import plot_field_grid_far_field


save_dir = '/home/<USER>/sneaky/data/imported_lens_and_field'

if not os.path.isdir(save_dir):
    os.makedirs(save_dir)


def has_file_with_prefix(folder_path, prefix):
    """
    Check if any file in the folder starts with the given prefix.

    :param folder_path: Path to the folder
    :param prefix: Prefix string to check for
    :return: True if a file with the prefix exists, False otherwise
    """
    if not os.path.isdir(folder_path):
        print(f"Error: '{folder_path}' is not a valid directory.")
        return False

    for filename in os.listdir(folder_path):
        if filename.startswith(prefix):
            return True
    return False

# Imported source from CST
source_field = ImportedFieldDistribution(cst_export_file_path=r'./input/e-field (f=193.75) [1]_subVolume.txt',
                                              cut_axis='z', cut_pos=20.05, skiprows=2, Nix=None, Niy=None)
source_field = TransverseField(distribution=source_field, z0=0, refractive_index=1.53, wavelength=1.55)
print(f"Source field resolution before re-meshing [um]: {source_field.res_x, source_field.res_y}")

source_field.translate_field_xy(delta_x=10, delta_y=0)
source_field.view()
plt.show()

monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_beam_radius_per_z=True)

# b_val = 32.5
# d_val = 20
# file_name = f'b={b_val:.2f}_d={d_val:.2f}'
# lens = SingleSurface(surface_type='EllipsoidalLensSurface',
#                      surface_parameters={'x0': 0, 'y0': 0, 'z0': d_val, 'major_half_axis': b_val,
#                                          'n_lens': 1.53, 'n_air': 1., 'focal_length': None, 'direction': 1},
#                      refractive_index=1.53, material_side=-1, priority=2, max_radius=None)
file_name = f'imported_lens_and_field_xy100_z5'
lens = ImportedModel(model_file_path=r'Lens_b2811_d1942_ExtendNegz.stl')
lens.rotate(theta_x_deg=180)

scene = CouplingScene(input_field=source_field, target_field=None,
                      background_material_index=1.,
                      optical_structures=[lens],
                      sim_size_xy=[100,100], sim_z_end=49.554 + 5,
                      wavelength_sampling_xy=8,
                      material_wavelength_sampling_z=8,
                      background_wavelength_sampling_z=4,
                      monitors=[monitor_xz],
                      boundary_condition='ABC', boundary_parameters=None)
scene.preview(mode='xz')
plt.show()

run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=False,
        name='optimized', sim_result_dir=None, opt_log_dir=None, show_plot=False)

plot_field_grid_far_field(scene.monitors[-1], folder_path=save_dir, prefix=file_name,
                          save_figure=True, save_data=True, show_plot=True)

plot_field_grid_far_field(monitor_xz, folder_path=save_dir, prefix=file_name+'_xz',
                          save_figure=True, save_data=True, show_plot=True)

