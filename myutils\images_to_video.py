import cv2
import os
import re
import numpy as np
from pathlib import Path


def natural_sort_key(text):
    """Sort function that handles numbers in filenames naturally (1, 2, 10 instead of 1, 10, 2)"""
    return [int(c) if c.isdigit() else c.lower() for c in re.split(r'(\d+)', text)]


def extract_z_position(filename):
    """Extract z position from filename format: index_z-position.extension"""
    # Match pattern like "55_-0.0701430073695348.tiff"
    match = re.search(r'_([+-]?\d+\.?\d*)', filename)
    if match:
        return float(match.group(1))
    return None


def add_z_position_overlay(img, z_position, font_scale=0.8, thickness=1):
    """Add z position text overlay to image"""
    if z_position is None:
        return img

    # Format z position to 3 decimal places
    z_text = f"Z: {z_position:.2f} um"

    # Get text size for positioning
    font = cv2.FONT_HERSHEY_SIMPLEX
    (text_width, text_height), _ = cv2.getTextSize(z_text, font, font_scale, thickness)

    # Position text in top-left corner with some padding
    x = 20
    y = 40

    # Add semi-transparent background rectangle
    overlay = img.copy()
    cv2.rectangle(overlay, (x - 10, y - text_height - 10), (x + text_width + 10, y + 10), (0, 0, 0), -1)
    img = cv2.addWeighted(img, 0.7, overlay, 0.3, 0)

    # Add text
    cv2.putText(img, z_text, (x, y), font, font_scale, (255, 255, 255), thickness)

    return img


def images_to_video(input_folder, output_video="output_video.mp4", fps=30, duration=None, show_z_position=True, image_extension=None):
    """
    Convert a sequence of images to a video

    Args:
        input_folder (str): Path to folder containing images
        output_video (str): Output video filename
        fps (int): Frames per second for output video
        duration (float): Optional duration in seconds (if None, uses all images)
        show_z_position (bool): Whether to display z position overlay
    """

    # Get all image files from the folder
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'} if image_extension is None else {image_extension}
    image_files = []

    for file in os.listdir(input_folder):
        if Path(file).suffix.lower() in image_extensions:
            image_files.append(file)

    if not image_files:
        print("No image files found in the specified folder!")
        return

    # Sort images naturally by filename
    image_files.sort(key=natural_sort_key)

    # If duration is specified, calculate how many images to use
    if duration:
        total_frames = int(duration * fps)
        if total_frames < len(image_files):
            # Use evenly spaced images
            indices = np.linspace(0, len(image_files) - 1, total_frames, dtype=int)
            image_files = [image_files[i] for i in indices]

    print(f"Found {len(image_files)} images")
    print(f"First few files: {image_files[:5]}")

    # Read the first image to get dimensions
    first_image_path = os.path.join(input_folder, image_files[0])
    first_image = cv2.imread(first_image_path)

    if first_image is None:
        print(f"Could not read the first image: {first_image_path}")
        return

    height, width, layers = first_image.shape
    print(f"Video dimensions: {width}x{height}")
    print(f"Output FPS: {fps}")

    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video, fourcc, fps, (width, height))

    # Process each image
    for i, filename in enumerate(image_files):
        image_path = os.path.join(input_folder, filename)
        img = cv2.imread(image_path)

        if img is None:
            print(f"Warning: Could not read image {filename}, skipping...")
            continue

        # Resize image if it doesn't match the expected dimensions
        if img.shape[:2] != (height, width):
            img = cv2.resize(img, (width, height))

        # Add z position overlay if enabled
        if show_z_position:
            z_pos = extract_z_position(filename)
            z_pos *= 1e3    # um
            img = add_z_position_overlay(img, z_pos)

        out.write(img)

        # Progress indicator
        if (i + 1) % 10 == 0 or i == len(image_files) - 1:
            print(f"Processed {i + 1}/{len(image_files)} images")

    # Release everything
    out.release()
    cv2.destroyAllWindows()

    print(f"Video saved as: {output_video}")
    print(f"Total frames: {len(image_files)}")
    print(f"Duration: {len(image_files) / fps:.2f} seconds")


if __name__ == "__main__":
    input_folder = input("Enter the path to your images folder: ").strip()

    if not os.path.exists(input_folder):
        print("Folder does not exist!")
        exit(1)

    output_name = "output_video.mp4"
    output_path = os.path.join(input_folder, output_name)
    fps = 10
    duration = None

    if not os.path.exists(input_folder):
        print("Folder does not exist!")
        exit(1)

    # Create the video
    images_to_video(input_folder=input_folder, output_video=output_path, fps=fps, duration=duration, show_z_position=True, image_extension='.tiff')