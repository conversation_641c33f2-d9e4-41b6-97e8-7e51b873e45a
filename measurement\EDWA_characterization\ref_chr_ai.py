import traceback
import numpy as np
import time
import threading
import queue
from datetime import datetime
import matplotlib.pyplot as plt
import os

from device_wrappers.Newfocus_TLB6700 import NewFocus_TLB6700_wrapper
from device_wrappers.Thorlabs_PMxxx.PMxxx_wrapper import PMxxx


class ReflectivityCharacterization:
    def __init__(self):
        self.start_wl = 1520
        self.end_wl = 1550
        self.fwd_scan_speed = 10.0      # 20 nm/s is the max.
        self.bwd_scan_speed = 10.0
        self.laser_power = 3

        self.ecl = NewFocus_TLB6700_wrapper.NewFocus6700(id=4106, key='6700 SN70006')
        self.ecl.connect_laser(True)
        self.init_ecl()

        self.pd = PMxxx()
        self.init_pd()

        # Data collection attributes
        self.measurement_data = []
        self.measurement_queue = queue.Queue()
        self.stop_measurement = threading.Event()
        self.measurement_thread = None

    def init_ecl(self):
        # self.ecl.reset()
        self.ecl.set_wl_scan_speed(wl_scan_speed_forward=self.fwd_scan_speed, wl_scan_speed_return=self.bwd_scan_speed)
        self.ecl.set_wl_scan_limit(wl_scan_limit_value=[self.start_wl, self.end_wl])
        self.ecl.set_lbd(Lambda=self.start_wl)
        self.ecl.set_pzt_voltage(0)
        self.ecl.set_out_power(power_mW=self.laser_power)

    def init_pd(self):
        self.pd.wavelength = 1550
        self.pd.auto_range = False
        self.pd.set_power_range(value_W=3.9e-3)     # TODO: check the power range
        self.pd.set_average(1)  # Reduced averaging for faster response during scanning

    def cal_scan_time(self):
        # wl_lims  = self.ecl.query_wl_scan_limit()
        # print(f"Wavelength limits: {wl_lims}")    # empty
        wl_span = self.end_wl - self.start_wl
        # frd_speed, bwd_speed = self.ecl.query_scan_speed()    # empty
        # scan_config = self.ecl.get_scan_config()
        # print(f"Scan config: {scan_config}")
        scan_time = wl_span / self.fwd_scan_speed
        if self.bwd_scan_speed != 0:
            scan_time += wl_span / self.bwd_scan_speed  

        return scan_time

    def continuous_pd_measurement(self, measurement_interval=0.001):
        """
        Continuously measure PD power in a separate thread.

        Args:
            measurement_interval: Time between measurements in seconds (default: 1ms)
        """
        print("Starting continuous PD measurement...")
        while not self.stop_measurement.is_set():
            try:
                timestamp = time.time()
                # Try to get current wavelength from laser
                try:
                    current_wl = self.ecl.query_lbd()
                except:
                    current_wl = None

                # Get power measurement
                power = self.pd.get_power()

                # Store measurement with timestamp
                measurement = {
                    'timestamp': timestamp,
                    'wavelength': current_wl,
                    'power_dbm': power,
                    'datetime': datetime.fromtimestamp(timestamp)
                }

                self.measurement_queue.put(measurement)

                # Sleep for the specified interval
                time.sleep(measurement_interval)

            except Exception as e:
                print(f"Error in continuous measurement: {e}")
                time.sleep(measurement_interval)

        print("Continuous PD measurement stopped.")

    def start_sweeping_with_measurement(self, measurement_interval=0.001):
        """
        Start wavelength sweeping with concurrent PD measurements.

        Args:
            measurement_interval: Time between PD measurements in seconds
        """
        scan_time = self.cal_scan_time()
        print(f"Scan time: {scan_time} seconds")
        print(f"Measurement interval: {measurement_interval} seconds")

        self.ecl.set_output_state(value=True)

        # Clear previous data
        self.measurement_data = []
        while not self.measurement_queue.empty():
            self.measurement_queue.get()

        # Reset stop event
        self.stop_measurement.clear()

        # Start continuous measurement thread
        self.measurement_thread = threading.Thread(
            target=self.continuous_pd_measurement,
            args=(measurement_interval,)
        )
        self.measurement_thread.start()

        # Start laser scanning
        print("Starting laser scan...")
        self.ecl.start_wl_scan_process(True)    # the scan always go forth and back

        # Wait for scan to complete
        time.sleep(scan_time + 3)

        # Stop measurements
        self.stop_measurement.set()
        self.measurement_thread.join()

        # Collect all measurements from queue
        print("Collecting measurement data...")
        while not self.measurement_queue.empty():
            self.measurement_data.append(self.measurement_queue.get())

        print(f"Collected {len(self.measurement_data)} measurements")
        return self.measurement_data

    def start_sweeping(self):
        """Legacy method for backward compatibility"""
        return self.start_sweeping_with_measurement()

    def get_measurement_arrays(self, valid_only=False):
        """
        Extract measurement data into numpy arrays for analysis.

        Args:
            valid_only: If True, only return measurements with valid wavelength and power data

        Returns:
            tuple: (timestamps, wavelengths, powers) as numpy arrays
        """
        if not self.measurement_data:
            return np.array([]), np.array([]), np.array([])

        if valid_only:
            # Filter to only valid measurements
            valid_measurements = []
            for m in self.measurement_data:
                # Check wavelength validity (handle both string and numeric types)
                wl_valid = False
                if m['wavelength'] is not None:
                    try:
                        wl_float = float(m['wavelength'])
                        wl_valid = not np.isnan(wl_float)
                    except (ValueError, TypeError):
                        wl_valid = False

                # Check power validity
                power_valid = False
                try:
                    power_valid = not np.isnan(m['power_dbm'])
                except (TypeError, ValueError):
                    power_valid = False

                if wl_valid and power_valid:
                    valid_measurements.append(m)

            if not valid_measurements:
                return np.array([]), np.array([]), np.array([])

            timestamps = np.array([m['timestamp'] for m in valid_measurements])
            wavelengths = np.array([float(m['wavelength']) for m in valid_measurements])
            powers = np.array([m['power_dbm'] for m in valid_measurements])
        else:
            timestamps = np.array([m['timestamp'] for m in self.measurement_data])
            # Convert wavelengths to float, handling string values and None
            wavelengths = []
            for m in self.measurement_data:
                if m['wavelength'] is not None:
                    try:
                        wavelengths.append(float(m['wavelength']))
                    except (ValueError, TypeError):
                        wavelengths.append(np.nan)
                else:
                    wavelengths.append(np.nan)
            wavelengths = np.array(wavelengths)
            powers = np.array([m['power_dbm'] for m in self.measurement_data])

        return timestamps, wavelengths, powers

    def save_measurement_data(self, filename, save_only_valid=True):
        """
        Save measurement data to a CSV file.

        Args:
            filename: Path to save the CSV file
            save_only_valid: If True, only save measurements with valid wavelength and power data
        """
        if not self.measurement_data:
            print("No measurement data to save")
            return

        import csv

        # Filter data if requested
        if save_only_valid:
            valid_data = []
            for measurement in self.measurement_data:
                # Check wavelength validity (handle both string and numeric types)
                wl_valid = False
                if measurement['wavelength'] is not None:
                    try:
                        wl_float = float(measurement['wavelength'])
                        wl_valid = not np.isnan(wl_float)
                    except (ValueError, TypeError):
                        wl_valid = False

                # Check power validity
                power_valid = False
                try:
                    power_valid = not np.isnan(measurement['power_dbm'])
                except (TypeError, ValueError):
                    power_valid = False

                if wl_valid and power_valid:
                    valid_data.append(measurement)

            data_to_save = valid_data
            print(f"Filtered {len(self.measurement_data)} measurements to {len(valid_data)} valid measurements")
        else:
            data_to_save = self.measurement_data

        if not data_to_save:
            print("No valid data to save")
            return

        with open(filename, 'w', newline='') as csvfile:
            fieldnames = ['timestamp', 'datetime', 'wavelength_nm', 'power_dbm']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for measurement in data_to_save:
                # Convert wavelength to float for CSV output
                wl_value = measurement['wavelength']
                if wl_value is not None:
                    try:
                        wl_value = float(wl_value)
                    except (ValueError, TypeError):
                        wl_value = None

                writer.writerow({
                    'timestamp': measurement['timestamp'],
                    'datetime': measurement['datetime'].isoformat(),
                    'wavelength_nm': wl_value,
                    'power_dbm': measurement['power_dbm']
                })

        print(f"Saved {len(data_to_save)} measurements to {filename}")

    def plot_power_vs_wavelength(self, filename=None, show_plot=True, separate_directions=True,
                                 figsize=(12, 8), dpi=150):
        """
        Plot photodiode power measurements as a function of wavelength.

        Args:
            filename: Path to save the plot (PNG format). If None, auto-generates filename.
            show_plot: Whether to display the plot interactively
            separate_directions: If True, try to separate forward and backward scan directions
            figsize: Figure size as (width, height) in inches
            dpi: Resolution for saved plot

        Returns:
            str: Path to saved plot file
        """
        # Get valid measurement data
        timestamps, wavelengths, powers = self.get_measurement_arrays(valid_only=True)

        if len(wavelengths) == 0:
            print("No valid measurement data to plot")
            return None

        # Create figure and axis
        fig, ax = plt.subplots(figsize=figsize)

        if separate_directions and len(wavelengths) > 10:
            # Try to separate forward and backward scans
            forward_data, backward_data = self._separate_scan_directions(timestamps, wavelengths, powers)

            if len(forward_data[1]) > 0:
                ax.plot(forward_data[1], forward_data[2], 'b.-', markersize=2, linewidth=1,
                       label=f'Forward scan ({len(forward_data[1])} points)', alpha=0.8)

            if len(backward_data[1]) > 0:
                ax.plot(backward_data[1], backward_data[2], 'r.-', markersize=2, linewidth=1,
                       label=f'Backward scan ({len(backward_data[1])} points)', alpha=0.8)

            if len(forward_data[1]) > 0 and len(backward_data[1]) > 0:
                ax.legend()
        else:
            # Plot all data together
            ax.plot(wavelengths, powers, 'b.-', markersize=2, linewidth=1,
                   label=f'All data ({len(wavelengths)} points)', alpha=0.8)
            ax.legend()

        # Formatting
        ax.set_xlabel('Wavelength (nm)', fontsize=12)
        ax.set_ylabel('Power (dBm)', fontsize=12)
        ax.set_title(f'Photodiode Power vs Wavelength\n'
                    f'Scan: {np.min(wavelengths):.3f} - {np.max(wavelengths):.3f} nm, '
                    f'Power: {np.min(powers):.2f} - {np.max(powers):.2f} dBm', fontsize=14)
        ax.grid(True, alpha=0.3)

        # Set reasonable axis limits
        wl_margin = (np.max(wavelengths) - np.min(wavelengths)) * 0.02
        power_margin = (np.max(powers) - np.min(powers)) * 0.05
        ax.set_xlim(np.min(wavelengths) - wl_margin, np.max(wavelengths) + wl_margin)
        ax.set_ylim(np.min(powers) - power_margin, np.max(powers) + power_margin)

        # Add measurement info as text
        info_text = (f'Measurements: {len(wavelengths)} valid points\n'
                    f'Wavelength span: {np.max(wavelengths) - np.min(wavelengths):.3f} nm\n'
                    f'Power range: {np.max(powers) - np.min(powers):.2f} dB')
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        # Save plot
        if filename is None:
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"power_vs_wavelength_{timestamp_str}.png"

        # Ensure directory exists
        plot_dir = os.path.dirname(filename) if os.path.dirname(filename) else '.'
        os.makedirs(plot_dir, exist_ok=True)

        plt.savefig(filename, dpi=dpi, bbox_inches='tight')
        print(f"Plot saved to: {filename}")

        # Show plot if requested
        if show_plot:
            plt.show()
        else:
            plt.close()

        return filename

    def _separate_scan_directions(self, timestamps, wavelengths, powers):
        """
        Attempt to separate forward and backward scan directions based on wavelength trends.

        Args:
            timestamps, wavelengths, powers: Arrays of measurement data

        Returns:
            tuple: ((t_fwd, wl_fwd, p_fwd), (t_bwd, wl_bwd, p_bwd))
        """
        if len(wavelengths) < 10:
            return (timestamps, wavelengths, powers), (np.array([]), np.array([]), np.array([]))

        # Find the turning point (where wavelength direction changes)
        # Look for the maximum wavelength as the likely turning point
        max_wl_idx = np.argmax(wavelengths)

        # Split data at the turning point
        # Forward scan: start to maximum wavelength
        forward_mask = np.arange(len(wavelengths)) <= max_wl_idx
        # Backward scan: maximum wavelength to end
        backward_mask = np.arange(len(wavelengths)) >= max_wl_idx

        # Extract forward scan data
        t_fwd = timestamps[forward_mask]
        wl_fwd = wavelengths[forward_mask]
        p_fwd = powers[forward_mask]

        # Extract backward scan data
        t_bwd = timestamps[backward_mask]
        wl_bwd = wavelengths[backward_mask]
        p_bwd = powers[backward_mask]

        # Verify that we actually have both directions
        # Check if wavelength is generally increasing in forward and decreasing in backward
        if len(wl_fwd) > 5:
            fwd_trend = np.polyfit(np.arange(len(wl_fwd)), wl_fwd, 1)[0]
        else:
            fwd_trend = 0

        if len(wl_bwd) > 5:
            bwd_trend = np.polyfit(np.arange(len(wl_bwd)), wl_bwd, 1)[0]
        else:
            bwd_trend = 0

        # If trends don't make sense, return all data as forward scan
        if fwd_trend <= 0 or bwd_trend >= 0:
            return (timestamps, wavelengths, powers), (np.array([]), np.array([]), np.array([]))

        return (t_fwd, wl_fwd, p_fwd), (t_bwd, wl_bwd, p_bwd)

    def quick_plot(self, show=True):
        """
        Quick plotting function with default settings.

        Args:
            show: Whether to display the plot interactively

        Returns:
            str: Path to saved plot file
        """
        return self.plot_power_vs_wavelength(show_plot=show)

    def plot_measurement_summary(self, filename=None, show_plot=True):
        """
        Create a comprehensive summary plot with multiple subplots.

        Args:
            filename: Path to save the plot. If None, auto-generates filename.
            show_plot: Whether to display the plot interactively

        Returns:
            str: Path to saved plot file
        """
        timestamps_all, wavelengths_all, powers_all = self.get_measurement_arrays()
        timestamps_valid, wavelengths_valid, powers_valid = self.get_measurement_arrays(valid_only=True)

        if len(timestamps_all) == 0:
            print("No measurement data to plot")
            return None

        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # Plot 1: Power vs Time (all data)
        ax1.plot(timestamps_all - timestamps_all[0], powers_all, 'b.-', markersize=1, alpha=0.7)
        ax1.set_xlabel('Time (s)')
        ax1.set_ylabel('Power (dBm)')
        ax1.set_title(f'Power vs Time (All {len(timestamps_all)} measurements)')
        ax1.grid(True, alpha=0.3)

        # Plot 2: Wavelength vs Time (valid data only)
        if len(wavelengths_valid) > 0:
            ax2.plot(timestamps_valid - timestamps_all[0], wavelengths_valid, 'g.-', markersize=1, alpha=0.7)
            ax2.set_xlabel('Time (s)')
            ax2.set_ylabel('Wavelength (nm)')
            ax2.set_title(f'Wavelength vs Time (Valid {len(wavelengths_valid)} measurements)')
            ax2.grid(True, alpha=0.3)
        else:
            ax2.text(0.5, 0.5, 'No valid wavelength data', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('Wavelength vs Time (No valid data)')

        # Plot 3: Power vs Wavelength (main plot)
        if len(wavelengths_valid) > 0:
            ax3.plot(wavelengths_valid, powers_valid, 'r.-', markersize=2, alpha=0.8)
            ax3.set_xlabel('Wavelength (nm)')
            ax3.set_ylabel('Power (dBm)')
            ax3.set_title('Power vs Wavelength (Valid data)')
            ax3.grid(True, alpha=0.3)
        else:
            ax3.text(0.5, 0.5, 'No valid data for\npower vs wavelength', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('Power vs Wavelength (No valid data)')

        # Plot 4: Data validity statistics
        valid_count = len(timestamps_valid)
        invalid_count = len(timestamps_all) - valid_count

        if invalid_count > 0:
            ax4.pie([valid_count, invalid_count], labels=['Valid', 'Invalid'],
                   autopct='%1.1f%%', colors=['lightgreen', 'lightcoral'])
            ax4.set_title('Data Validity')
        else:
            ax4.pie([valid_count], labels=['Valid'], autopct='%1.1f%%', colors=['lightgreen'])
            ax4.set_title('Data Validity (100% valid)')

        plt.tight_layout()

        # Save plot
        if filename is None:
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"measurement_summary_{timestamp_str}.png"

        plt.savefig(filename, dpi=150, bbox_inches='tight')
        print(f"Summary plot saved to: {filename}")

        if show_plot:
            plt.show()
        else:
            plt.close()

        return filename

    def close(self):
        # Stop any ongoing measurements
        if hasattr(self, 'stop_measurement'):
            self.stop_measurement.set()
        if hasattr(self, 'measurement_thread') and self.measurement_thread:
            self.measurement_thread.join(timeout=1.0)

        self.ecl.set_output_state(value=False)
        self.pd.close()


if __name__ == '__main__':
    try:
        reflectivity_char = ReflectivityCharacterization()

        # Start sweeping with concurrent PD measurements
        # Measurement interval of 1ms gives ~1000 measurements per second
        measurement_data = reflectivity_char.start_sweeping_with_measurement(measurement_interval=0.001)

        # Analyze all data
        timestamps_all, wavelengths_all, powers_all = reflectivity_char.get_measurement_arrays()

        # Get only valid data
        timestamps_valid, wavelengths_valid, powers_valid = reflectivity_char.get_measurement_arrays(valid_only=True)

        print(f"\nMeasurement Summary:")
        print(f"Total measurements: {len(measurement_data)}")
        print(f"Valid measurements: {len(timestamps_valid)} ({len(timestamps_valid)/len(measurement_data)*100:.1f}%)")

        if len(timestamps_all) > 1:
            print(f"Time span: {timestamps_all[-1] - timestamps_all[0]:.2f} seconds")
            print(f"Average measurement rate: {len(measurement_data)/(timestamps_all[-1] - timestamps_all[0]):.1f} Hz")

        if len(wavelengths_valid) > 0:
            print(f"Wavelength range: {np.min(wavelengths_valid):.3f} - {np.max(wavelengths_valid):.3f} nm")
            print(f"Power range: {np.min(powers_valid):.2f} - {np.max(powers_valid):.2f} dBm")
        else:
            print("Warning: No valid measurements with both wavelength and power data")

        # Save only valid data to file
        timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"reflectivity_scan_{timestamp_str}.csv"
        reflectivity_char.save_measurement_data(filename, save_only_valid=True)

        # Create and save plot
        if len(wavelengths_valid) > 0:
            plot_filename = f"reflectivity_scan_{timestamp_str}.png"
            reflectivity_char.plot_power_vs_wavelength(
                filename=plot_filename,
                show_plot=True,  # Set to True if you want to see the plot interactively
                separate_directions=True
            )
        else:
            print("No valid data available for plotting")

    except Exception as e:
        print(f"Error: {traceback.format_exc()}")
    finally:
        if 'reflectivity_char' in locals():
            reflectivity_char.close()