import matplotlib.pyplot as plt
import numpy as np
import time
import keyboard  # using module keyboard
import NewFocus_TLB6700_wrapper


wl_scan_limit = [1520, 1570]

def main():

    tlb6700 = NewFocus_TLB6700_wrapper.NewFocus6700(id=4106, key='6700 SN70006')
    tlb6700.connect_laser(True)
    print("First error caught: {}".format(tlb6700.error))
    old_lbd = tlb6700.query_lbd()
    pzt = tlb6700.query_pzt_voltage()
    tlb6700.set_lbd(Lambda=1560.11)
    tlb6700.set_pzt_voltage(pzt_voltage=50.5)
    tlb6700.set_out_power(power_mW=1)
    tlb6700.set_output_state(value=True)
    print(tlb6700.query_wl_scan_limit())
    print(tlb6700.query_scan_speed())
    tlb6700.set_wl_scan_speed(wl_scan_speed=1.0) # set the wavelength scan speed to be 1 nm/s, the return speed is 0.1 nm/s
    tlb6700.set_wl_scan_limit(wl_scan_limit_value=wl_scan_limit)
    tlb6700.start_wl_scan_process(value=False)
    print('Laser wavelength:')
    print("\t{}".format(old_lbd))
    print('Laser piezo:')
    print("\t{}".format(pzt))

    PZT_Volt = float(pzt)
    labda_set = float(old_lbd)
    while True:  # making a loop
        if keyboard.is_pressed('up arrow'):  # if key 'arrow up' is pressed
            if PZT_Volt > 0.00 and PZT_Volt < 100.00:
                pzt = tlb6700.query_pzt_voltage()
                PZT_Volt = float(pzt)
                PZT_Volt = PZT_Volt + 0.1
                time.sleep(0.2)
                tlb6700.set_pzt_voltage(pzt_voltage=PZT_Volt)

        elif keyboard.is_pressed('down arrow'):  # if key 'arrow down' is pressed
            if PZT_Volt > 0.00 and PZT_Volt < 100.00:
                pzt = tlb6700.query_pzt_voltage()
                PZT_Volt = float(pzt)
                PZT_Volt = PZT_Volt - 0.1
                time.sleep(0.2)
                tlb6700.set_pzt_voltage(pzt_voltage=PZT_Volt)

        elif keyboard.is_pressed('c'):  # if key 'arrow left' is pressed
                if labda_set > 1520.00 and labda_set < 1570.00:
                    labda_set = labda_set - 0.01
                    time.sleep(0.2)
                    tlb6700.set_lbd(Lambda=labda_set)

        elif keyboard.is_pressed('v'):  # if key 'arrow right' is pressed
            if labda_set > 1520.00 and labda_set < 1570.00:
                labda_set = labda_set + 0.01
                time.sleep(0.2)
                tlb6700.set_lbd(Lambda=labda_set)

        elif keyboard.is_pressed('u'):  # if key 'arrow up' is pressed
            if PZT_Volt > 0.00 and PZT_Volt < 100.00:
                pzt = tlb6700.query_pzt_voltage()
                PZT_Volt = float(pzt)
                PZT_Volt = PZT_Volt + 0.01
                time.sleep(0.2)
                tlb6700.set_pzt_voltage(pzt_voltage=PZT_Volt)

        elif keyboard.is_pressed('d'):  # if key 'arrow down' is pressed
            if PZT_Volt > 0.00 and PZT_Volt < 100.00:
                pzt = tlb6700.query_pzt_voltage()
                PZT_Volt = float(pzt)
                PZT_Volt = PZT_Volt - 0.01
                time.sleep(0.2)
                tlb6700.set_pzt_voltage(pzt_voltage=PZT_Volt)


if __name__ == '__main__':
    main()