import csv
import os.path

import numpy as np
import matplotlib.pyplot as plt
from itertools import islice

from process_measurement.utils.formatting import define_scale, format_large_number
from utils import read_csv


def plot_RF_spectrum(x_values, y_values, title='', font_size=14, saving_dir=None):
    scale = define_scale(min(min(x_values), min(x_values)))
    x_values = format_large_number(x_values, scale=scale)

    fig, ax = plt.subplots(figsize=(8, 6))
    ax.plot(x_values, y_values, '-')
    title = title if title else 'RF Spectrum'
    ax.set_title(title, fontsize=font_size+2)
    ax.set_xlabel(f'Frequency ({scale}Hz)', fontsize=font_size)
    ax.set_ylabel('Power (dBm)', fontsize=font_size)
    ax.set_ylim([min(y_values) - 1, max(y_values) + 1])
    ax.set_xlim([min(x_values), max(x_values)])
    ax.tick_params(axis='x', labelsize=font_size)
    ax.tick_params(axis='y', labelsize=font_size)
    ax.grid(True)
    plt.tight_layout()
    if saving_dir:
        fig.savefig(fname=os.path.join(saving_dir, title + '.png'), dpi=1200)
    plt.show()


def compare_spectrums(x1, y1, label1, x2, y2, label2, title='', font_size=14, saving_dir=None):
    fig, ax1 = plt.subplots(figsize=(8, 6))
    # color1, color2 = 'tab:blue', 'tab:orange'
    color1, color2 = 'blue', 'red'

    scale = define_scale(min(min(x1), min(x2)))
    x1 = format_large_number(x1, scale=scale)
    x2 = format_large_number(x2, scale=scale)

    # Plot the first dataset
    ax1.plot(x1, y1, label=label1, color=color1)
    ax1.set_xlabel(f'Frequency ({scale}Hz)', color=color1, fontsize=font_size)
    ax1.set_ylabel('Power (dBm)', fontsize=font_size)
    # ax1.set_ylim([min(min(y1), min(y2)) - 1, max(max(y1), max(y2)) + 1])  # Common y-axis range
    ax1.set_ylim([min(min(y1), min(y2)) - 1, -35])  # Common y-axis range
    ax1.set_xlim([min(x1), max(x1)])
    ax1.tick_params(axis='x', colors=color1, labelsize=font_size)
    ax1.tick_params(axis='y', labelsize=font_size)
    # ax1.spines['bottom'].set_color(color1)

    # Create a second x-axis on the top
    ax2 = ax1.twiny()
    ax2.plot(x2, y2, label=label2, color=color2)
    ax2.set_xlabel(f'Frequency ({scale}Hz)', color=color2, fontsize=font_size)
    ax2.tick_params(axis='x', colors=color2, labelsize=font_size)
    # ax2.spines['top'].set_color(color2)
    ax2.set_xlim([min(x2), max(x2)])
    # ax2.set_xlim([min(x2) - 0.001839, max(x2) - 0.001839])

    title = title if title else 'PNA RF Spectrum'
    fig.suptitle(title, fontsize=font_size+2)

    # Show the legend
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=font_size-2)
    ax1.grid(True)
    plt.tight_layout()

    if saving_dir:
        fig.savefig(fname=os.path.join(saving_dir, title + '.png'), dpi=1200)

    plt.show()


def convert_to_relative(x_in):
    center_x = x_in[int(len(x_in)/2)]
    x_rel = x_in - center_x
    return x_rel


def compare_files():
    dir_fr = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\09_RF_spectrum'
    dir_locked = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\09_RF_spectrum'

    wl_id = 0
    wls = [1533, 1539]

    free_running = [
        r'RF_spec_free_running_QDMLLD_49p7GHz_LaserCurrent_225mA_span10M_RBW50k_wo_amplifier_rec1.csv',
    ]
    locked = [
        r'RF_spec_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad18p0mA_RF_linewidth_span_RBW50k_10Mrec1.csv',
    ]
    file_free_running = os.path.join(dir_fr, free_running[wl_id])
    file_locked = os.path.join(dir_locked, locked[wl_id])

    plot_dir = dir_fr

    x1, y1 = read_csv(file_free_running, start_line=31, nr_points=1001, delimiter='\t')
    x2, y2 = read_csv(file_locked, start_line=31, nr_points=1001, delimiter='\t')
    # x1 = convert_to_relative(x1)
    # x2 = convert_to_relative(x2)

    compare_spectrums(x1, y1, 'w/o feedback', x2, y2, 'w/ feedback',
                      font_size=18,
                      title=f'Reference laser around {wls[wl_id]} nm',
                      saving_dir=plot_dir)


def plot_single_RF_spectrum():
    file_path = r'I:\Recordings\Bao\CLEO2025\02_FSR_over_current\RF_over_current_rec3\RF_spec_FSR_measurement_laser_current_225.0_mA_LaserTec_25deg.csv'
    # plot_saving_dir = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025'
    plot_saving_dir=None
    x_values, y_values = read_csv(file_path, start_line=31, nr_points=1001)
    plot_RF_spectrum(x_values, y_values, title="Locked Spectrum", font_size=22, saving_dir=plot_saving_dir)


def plot_RF_spectrum_CLEO(saving_dir=None):
    plt.rcParams['font.family'] = 'arial'
    plt.rcParams['font.size'] = 5
    folder_path = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\09_RF_spectrum'
    free_running = r'RF_spec_free_running_QDMLLD_49p7GHz_LaserCurrent_225mA_span10M_RBW50k_wo_amplifier_rec1.csv'
    locked = r'RF_spec_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad18p0mA_RF_linewidth_span_RBW50k_10Mrec1.csv'
    x_fr, y_fr = read_csv(os.path.join(folder_path, free_running), start_line=31, nr_points=1001)
    x_l, y_l = read_csv(os.path.join(folder_path, locked), start_line=31, nr_points=1001)

    max_fr_id = np.argmax(y_fr)
    fr_center_freq = x_fr[max_fr_id] / 1e9
    print(f"Center freq. w/o feedback {fr_center_freq}")
    max_l_id = np.argmax(y_l)
    locked_center_freq = x_l[max_l_id] / 1e9
    print(f"Center freq. w feedback {locked_center_freq}")
    # x_l -= 49.7306e9
    # x_fr -= 49.728114e9
    scale = define_scale(min(min(x_fr), min(x_fr)))
    x_fr = format_large_number(x_fr, scale=scale)
    x_l = format_large_number(x_l, scale=scale)

    fig, ax = plt.subplots(figsize=(1.5, 1))
    ax.plot(x_fr, y_fr, 'tab:blue', linestyle='-', linewidth=0.5, label='w/o feedback')
    ax.plot(x_l, y_l, 'tab:purple', linestyle='-', linewidth=0.5, label='w/ feedback')
    # ax.plot(x_fr, y_fr, linestyle='-', linewidth=0.8, label='w/o feedback')
    # ax.plot(x_l, y_l, linestyle='-', linewidth=0.8, label='w/ feedback')
    # ax.set_xlabel(f'Frequency ({scale}Hz)')
    # ax.set_ylabel('Power (dBm)')
    # ax.set_ylim([min(y_values) - 1, max(y_values) + 1])
    ax.set_xlim([49.727, 49.732])
    ax.set_ylim([-100, -42])
    # ax.tick_params(axis='x')
    # ax.tick_params(axis='y')
    plt.tick_params(left=False, right=False, bottom=False, labelleft=True, labelbottom=True)
    ax.grid(True)
    for spine in ax.spines.values():
        spine.set_linewidth(1.0)  # Set line width
        # spine.set_alpha(0.5)  # Set transparency (0.5 for 50% transparent)
        spine.set_color('gray')
    # ax.legend(frameon=False, loc='upper right', handlelength=1, fontsize=3)
    plt.tight_layout()
    fig.savefig(fname=os.path.join(folder_path, 'RF_spectrum_comparison.png'), dpi=1200, transparent=True)
    plt.show()


if __name__ == "__main__":
    # plot_single_RF_spectrum()
    # compare_files()
    plot_RF_spectrum_CLEO()
