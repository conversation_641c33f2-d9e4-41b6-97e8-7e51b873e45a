"""
Signal Processing Module for Baseline Removal and Trend Analysis

This module provides comprehensive tools for separating slow-varying baseline trends
from fast-changing signal components in measurement data.

Author: AI Assistant
Date: 2025-07-16
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import signal
from scipy.optimize import curve_fit
from scipy.ndimage import uniform_filter1d
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LinearRegression
from sklearn.pipeline import Pipeline
import warnings
from typing import Tuple, Optional, Union, Dict, Any


class BaselineRemover:
    """
    A comprehensive class for baseline removal and signal detrending.
    
    Supports multiple methods:
    - Polynomial detrending
    - Moving average baseline estimation
    - High-pass filtering
    - Sa<PERSON><PERSON>ky-Golay filtering
    - Rolling statistics
    - Frequency domain filtering
    """
    
    def __init__(self, x_data: np.ndarray, y_data: np.ndarray, ignore_deepest_peak: bool = False):
        """
        Initialize the BaselineRemover with measurement data.

        Args:
            x_data: Independent variable (e.g., wavelength, time, piezo percentage)
            y_data: Dependent variable (e.g., power measurements)
            ignore_deepest_peak: If True, excludes the deepest peak from baseline estimation
        """
        self.x_data = np.asarray(x_data)
        self.y_data = np.asarray(y_data)
        self.ignore_deepest_peak = ignore_deepest_peak

        # Validate input data
        if len(self.x_data) != len(self.y_data):
            raise ValueError("x_data and y_data must have the same length")

        if len(self.x_data) < 10:
            raise ValueError("Data must contain at least 10 points for meaningful analysis")

        # Remove NaN values
        valid_mask = ~(np.isnan(self.x_data) | np.isnan(self.y_data))
        self.x_data = self.x_data[valid_mask]
        self.y_data = self.y_data[valid_mask]

        # Sort data by x values for consistent processing
        sort_indices = np.argsort(self.x_data)
        self.x_data = self.x_data[sort_indices]
        self.y_data = self.y_data[sort_indices]

        # Storage for results
        self.baseline = None
        self.detrended = None
        self.method_used = None
        self.method_params = {}
        self.peak_mask = None  # Mask for excluded peak region

    def _find_deepest_peak_region(self, peak_width_fraction: float = 0.15,
                                 depth_threshold: float = 0.5) -> np.ndarray:
        """
        Find the region around the deepest peak to exclude from baseline fitting.

        Args:
            peak_width_fraction: Fraction of data length to use as peak width (default: 0.15)
            depth_threshold: Fraction of peak depth to use as threshold (default: 0.5)

        Returns:
            Boolean mask where True indicates points to exclude from baseline fitting
        """
        # Find the deepest point (minimum value)
        deepest_idx = np.argmin(self.y_data)
        deepest_value = self.y_data[deepest_idx]

        # Calculate peak width in terms of data points
        peak_width_points = max(10, int(len(self.y_data) * peak_width_fraction))
        half_width = peak_width_points // 2

        # Create initial mask for peak region
        peak_mask = np.zeros(len(self.y_data), dtype=bool)

        # Method 1: Simple window around deepest point
        start_idx = max(0, deepest_idx - half_width)
        end_idx = min(len(self.y_data), deepest_idx + half_width + 1)
        peak_mask[start_idx:end_idx] = True

        # Method 2: Estimate local baseline using edge regions
        # Use points at the edges of the data for baseline estimation
        edge_fraction = 0.2
        edge_points = int(len(self.y_data) * edge_fraction)

        left_edge = self.y_data[:edge_points]
        right_edge = self.y_data[-edge_points:]
        edge_baseline = np.median(np.concatenate([left_edge, right_edge]))

        # Calculate peak depth relative to edge baseline
        peak_depth = edge_baseline - deepest_value

        # Only proceed if we have a significant peak
        if peak_depth > 0.1:  # At least 0.1 dBm deep
            # Set threshold based on peak depth
            threshold = deepest_value + depth_threshold * peak_depth

            # Find all points below threshold
            below_threshold = self.y_data < threshold

            # Find the contiguous region containing the deepest point
            if below_threshold[deepest_idx]:
                # Extend left from deepest point
                left_idx = deepest_idx
                while left_idx > 0 and below_threshold[left_idx - 1]:
                    left_idx -= 1

                # Extend right from deepest point
                right_idx = deepest_idx
                while right_idx < len(self.y_data) - 1 and below_threshold[right_idx + 1]:
                    right_idx += 1

                # Update mask to include the extended region
                peak_mask = np.zeros(len(self.y_data), dtype=bool)
                peak_mask[left_idx:right_idx + 1] = True

                # Ensure minimum width
                if (right_idx - left_idx + 1) < peak_width_points:
                    center = (left_idx + right_idx) // 2
                    start_idx = max(0, center - half_width)
                    end_idx = min(len(self.y_data), center + half_width + 1)
                    peak_mask[start_idx:end_idx] = True

        # Debug information
        excluded_points = np.sum(peak_mask)
        if excluded_points > 0:
            print(f"   Peak detection: Excluding {excluded_points} points around deepest peak")
            print(f"   Peak depth: {peak_depth:.3f} dBm, Threshold: {threshold:.3f} dBm")
        else:
            print(f"   Peak detection: No significant peak found (depth: {peak_depth:.3f} dBm)")

        return peak_mask

    def _get_baseline_fitting_data(self):
        """
        Get x and y data for baseline fitting, excluding peak region if requested.

        Returns:
            Tuple of (x_fit, y_fit, fit_mask) where fit_mask indicates which points were used
        """
        if self.ignore_deepest_peak:
            if self.peak_mask is None:
                self.peak_mask = self._find_deepest_peak_region()

            # Use points outside the peak region for fitting
            fit_mask = ~self.peak_mask
            x_fit = self.x_data[fit_mask]
            y_fit = self.y_data[fit_mask]
        else:
            # Use all points
            fit_mask = np.ones(len(self.x_data), dtype=bool)
            x_fit = self.x_data
            y_fit = self.y_data

        return x_fit, y_fit, fit_mask
    
    def polynomial_detrend(self, degree: int = 3) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove baseline using polynomial fitting.

        Args:
            degree: Degree of polynomial to fit (default: 3)

        Returns:
            Tuple of (baseline, detrended_signal)
        """
        # Get data for fitting (excluding peak if requested)
        x_fit, y_fit, fit_mask = self._get_baseline_fitting_data()

        if degree >= len(x_fit):
            degree = len(x_fit) - 1
            warnings.warn(f"Polynomial degree reduced to {degree} due to insufficient fitting points")

        # Fit polynomial to selected data points
        coeffs = np.polyfit(x_fit, y_fit, degree)

        # Evaluate polynomial at all x points to get full baseline
        baseline = np.polyval(coeffs, self.x_data)
        detrended = self.y_data - baseline

        self.baseline = baseline
        self.detrended = detrended
        self.method_used = "polynomial"

        params = {"degree": degree, "coefficients": coeffs}
        if self.ignore_deepest_peak:
            params["excluded_peak_points"] = np.sum(~fit_mask)
            params["peak_exclusion"] = True

        self.method_params = params

        return baseline, detrended
    
    def moving_average_detrend(self, window_size: Optional[int] = None,
                              window_fraction: float = 0.1) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove baseline using moving average.

        Args:
            window_size: Size of moving average window (if None, calculated from window_fraction)
            window_fraction: Fraction of data length to use as window size (default: 0.1)

        Returns:
            Tuple of (baseline, detrended_signal)
        """
        if window_size is None:
            window_size = max(5, int(len(self.y_data) * window_fraction))

        # Ensure odd window size for symmetric filtering
        if window_size % 2 == 0:
            window_size += 1

        if self.ignore_deepest_peak:
            # For peak exclusion, use interpolation approach
            x_fit, y_fit, fit_mask = self._get_baseline_fitting_data()

            # Apply moving average to the fitting data
            if len(y_fit) >= window_size:
                baseline_fit = uniform_filter1d(y_fit, size=window_size, mode='nearest')
            else:
                # Fallback for insufficient data
                baseline_fit = y_fit.copy()

            # Interpolate baseline to all points
            baseline = np.interp(self.x_data, x_fit, baseline_fit)
        else:
            # Standard moving average
            baseline = uniform_filter1d(self.y_data, size=window_size, mode='nearest')

        detrended = self.y_data - baseline

        self.baseline = baseline
        self.detrended = detrended
        self.method_used = "moving_average"

        params = {"window_size": window_size, "window_fraction": window_fraction}
        if self.ignore_deepest_peak:
            x_fit, y_fit, fit_mask = self._get_baseline_fitting_data()
            params["excluded_peak_points"] = np.sum(~fit_mask)
            params["peak_exclusion"] = True

        self.method_params = params

        return baseline, detrended
    
    def savgol_detrend(self, window_length: Optional[int] = None,
                       polyorder: int = 3, window_fraction: float = 0.15) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove baseline using Savitzky-Golay filter.

        Args:
            window_length: Length of filter window (if None, calculated from window_fraction)
            polyorder: Order of polynomial used to fit samples (default: 3)
            window_fraction: Fraction of data length to use as window size (default: 0.15)

        Returns:
            Tuple of (baseline, detrended_signal)
        """
        if window_length is None:
            window_length = max(polyorder + 2, int(len(self.y_data) * window_fraction))

        # Ensure odd window length and valid polyorder
        if window_length % 2 == 0:
            window_length += 1

        if polyorder >= window_length:
            polyorder = window_length - 1
            warnings.warn(f"Polynomial order reduced to {polyorder} due to window length constraint")

        if self.ignore_deepest_peak:
            # For peak exclusion, use interpolation approach
            x_fit, y_fit, fit_mask = self._get_baseline_fitting_data()

            # Apply Savitzky-Golay filter to the fitting data
            if len(y_fit) >= window_length:
                baseline_fit = signal.savgol_filter(y_fit, window_length, polyorder)
            else:
                # Fallback for insufficient data
                baseline_fit = y_fit.copy()

            # Interpolate baseline to all points
            baseline = np.interp(self.x_data, x_fit, baseline_fit)
        else:
            # Standard Savitzky-Golay filter
            baseline = signal.savgol_filter(self.y_data, window_length, polyorder)

        detrended = self.y_data - baseline

        self.baseline = baseline
        self.detrended = detrended
        self.method_used = "savitzky_golay"

        params = {"window_length": window_length, "polyorder": polyorder}
        if self.ignore_deepest_peak:
            x_fit, y_fit, fit_mask = self._get_baseline_fitting_data()
            params["excluded_peak_points"] = np.sum(~fit_mask)
            params["peak_exclusion"] = True

        self.method_params = params

        return baseline, detrended
    
    def highpass_filter_detrend(self, cutoff_fraction: float = 0.1, 
                               filter_order: int = 4) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove baseline using high-pass filtering.
        
        Args:
            cutoff_fraction: Cutoff frequency as fraction of Nyquist frequency (default: 0.1)
            filter_order: Order of Butterworth filter (default: 4)
            
        Returns:
            Tuple of (baseline, detrended_signal)
        """
        # Design high-pass Butterworth filter
        sos = signal.butter(filter_order, cutoff_fraction, btype='high', output='sos')
        
        # Apply filter to get high-frequency components (detrended signal)
        detrended = signal.sosfiltfilt(sos, self.y_data)
        
        # Baseline is original minus high-frequency components
        baseline = self.y_data - detrended
        
        self.baseline = baseline
        self.detrended = detrended
        self.method_used = "highpass_filter"
        self.method_params = {"cutoff_fraction": cutoff_fraction, "filter_order": filter_order}
        
        return baseline, detrended
    
    def rolling_statistics_detrend(self, window_size: Optional[int] = None,
                                  statistic: str = 'median',
                                  window_fraction: float = 0.2) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove baseline using rolling statistics (median, mean, etc.).

        Args:
            window_size: Size of rolling window (if None, calculated from window_fraction)
            statistic: Type of statistic ('median', 'mean', 'min', 'max')
            window_fraction: Fraction of data length to use as window size (default: 0.2)

        Returns:
            Tuple of (baseline, detrended_signal)
        """
        if window_size is None:
            window_size = max(5, int(len(self.y_data) * window_fraction))

        if self.ignore_deepest_peak:
            # For peak exclusion, use interpolation approach
            x_fit, y_fit, fit_mask = self._get_baseline_fitting_data()

            # Apply rolling statistics to the fitting data
            y_fit_series = pd.Series(y_fit)

            if statistic == 'median':
                baseline_fit = y_fit_series.rolling(window=min(window_size, len(y_fit)),
                                                   center=True, min_periods=1).median().values
            elif statistic == 'mean':
                baseline_fit = y_fit_series.rolling(window=min(window_size, len(y_fit)),
                                                   center=True, min_periods=1).mean().values
            elif statistic == 'min':
                baseline_fit = y_fit_series.rolling(window=min(window_size, len(y_fit)),
                                                   center=True, min_periods=1).min().values
            elif statistic == 'max':
                baseline_fit = y_fit_series.rolling(window=min(window_size, len(y_fit)),
                                                   center=True, min_periods=1).max().values
            else:
                raise ValueError(f"Unsupported statistic: {statistic}")

            # Interpolate baseline to all points
            baseline = np.interp(self.x_data, x_fit, baseline_fit)
        else:
            # Standard rolling statistics
            y_series = pd.Series(self.y_data)

            if statistic == 'median':
                baseline = y_series.rolling(window=window_size, center=True, min_periods=1).median().values
            elif statistic == 'mean':
                baseline = y_series.rolling(window=window_size, center=True, min_periods=1).mean().values
            elif statistic == 'min':
                baseline = y_series.rolling(window=window_size, center=True, min_periods=1).min().values
            elif statistic == 'max':
                baseline = y_series.rolling(window=window_size, center=True, min_periods=1).max().values
            else:
                raise ValueError(f"Unsupported statistic: {statistic}")

        detrended = self.y_data - baseline

        self.baseline = baseline
        self.detrended = detrended
        self.method_used = "rolling_statistics"

        params = {"window_size": window_size, "statistic": statistic}
        if self.ignore_deepest_peak:
            x_fit, y_fit, fit_mask = self._get_baseline_fitting_data()
            params["excluded_peak_points"] = np.sum(~fit_mask)
            params["peak_exclusion"] = True

        self.method_params = params

        return baseline, detrended

    def adaptive_baseline_detrend(self, lambda_param: float = 1e6,
                                 p_param: float = 0.001) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove baseline using Asymmetric Least Squares (ALS) algorithm.
        This is particularly effective for spectra with peaks above baseline.

        Args:
            lambda_param: Smoothness parameter (larger = smoother baseline)
            p_param: Asymmetry parameter (0 < p < 1, smaller = more asymmetric)

        Returns:
            Tuple of (baseline, detrended_signal)
        """
        try:
            from scipy.sparse import diags
            from scipy.sparse.linalg import spsolve
        except ImportError:
            raise ImportError("scipy.sparse is required for adaptive baseline removal")

        y = self.y_data
        L = len(y)

        # Create difference matrix
        D = diags([1, -2, 1], [0, -1, -2], shape=(L, L-2))
        D = D.T @ D  # Second derivative matrix

        # Initialize weights
        w = np.ones(L)

        # Iterative algorithm
        for _ in range(10):  # Maximum 10 iterations
            W = diags(w, 0, shape=(L, L))
            Z = W + lambda_param * D
            baseline = spsolve(Z, w * y)

            # Update weights
            w = p_param * (y > baseline) + (1 - p_param) * (y < baseline)

        detrended = y - baseline

        self.baseline = baseline
        self.detrended = detrended
        self.method_used = "adaptive_als"
        self.method_params = {"lambda_param": lambda_param, "p_param": p_param}

        return baseline, detrended

    def auto_select_method(self, methods_to_try: Optional[list] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Automatically select the best baseline removal method based on signal characteristics.

        Args:
            methods_to_try: List of methods to evaluate (if None, uses default set)

        Returns:
            Tuple of (baseline, detrended_signal) using the best method
        """
        if methods_to_try is None:
            methods_to_try = ['polynomial', 'moving_average', 'savitzky_golay', 'rolling_statistics']

        best_method = None
        best_score = float('inf')
        best_results = None
        best_method_params = None

        # Store original state
        original_baseline = self.baseline
        original_detrended = self.detrended
        original_method = self.method_used
        original_params = self.method_params

        for method in methods_to_try:
            try:
                if method == 'polynomial':
                    baseline, detrended = self.polynomial_detrend(degree=3)
                elif method == 'moving_average':
                    baseline, detrended = self.moving_average_detrend()
                elif method == 'savitzky_golay':
                    baseline, detrended = self.savgol_detrend()
                elif method == 'highpass_filter':
                    baseline, detrended = self.highpass_filter_detrend()
                elif method == 'rolling_statistics':
                    baseline, detrended = self.rolling_statistics_detrend(statistic='median')
                elif method == 'adaptive_als':
                    baseline, detrended = self.adaptive_baseline_detrend()
                else:
                    continue

                # Score based on smoothness of baseline and variance of detrended signal
                baseline_smoothness = np.var(np.diff(baseline))
                detrended_variance = np.var(detrended)

                # Combined score (lower is better)
                score = baseline_smoothness + 0.1 * detrended_variance

                if score < best_score:
                    best_score = score
                    best_method = method
                    best_results = (baseline, detrended)
                    best_method_params = self.method_params.copy()

            except Exception as e:
                warnings.warn(f"Method {method} failed: {str(e)}")
                continue

        if best_results is None:
            raise RuntimeError("No baseline removal method succeeded")

        # Set the best results as the current state
        self.baseline = best_results[0]
        self.detrended = best_results[1]
        self.method_used = best_method
        self.method_params = best_method_params

        peak_info = ""
        if self.ignore_deepest_peak:
            excluded_points = self.method_params.get('excluded_peak_points', 0)
            if excluded_points > 0:
                peak_info = f" (excluding {excluded_points} peak points)"
            else:
                peak_info = " (no peak excluded)"

        print(f"Auto-selected method: {best_method}{peak_info} (score: {best_score:.2e})")
        return best_results

    def get_signal_statistics(self) -> Dict[str, float]:
        """
        Calculate statistics about the original signal and processed results.

        Returns:
            Dictionary containing various signal statistics
        """
        stats = {
            'original_mean': np.mean(self.y_data),
            'original_std': np.std(self.y_data),
            'original_range': np.ptp(self.y_data),
            'original_rms': np.sqrt(np.mean(self.y_data**2))
        }

        if self.baseline is not None:
            stats.update({
                'baseline_mean': np.mean(self.baseline),
                'baseline_std': np.std(self.baseline),
                'baseline_range': np.ptp(self.baseline),
                'baseline_trend': (self.baseline[-1] - self.baseline[0]) / len(self.baseline)
            })

        if self.detrended is not None:
            stats.update({
                'detrended_mean': np.mean(self.detrended),
                'detrended_std': np.std(self.detrended),
                'detrended_range': np.ptp(self.detrended),
                'detrended_rms': np.sqrt(np.mean(self.detrended**2)),
                'snr_improvement': np.std(self.y_data) / np.std(self.detrended) if np.std(self.detrended) > 0 else np.inf
            })

        return stats

    def plot_results(self, figsize: Tuple[int, int] = (12, 8),
                    save_path: Optional[str] = None,
                    show_plot: bool = True,
                    x_label: str = "X",
                    y_label: str = "Y") -> None:
        """
        Create comprehensive visualization of baseline removal results.

        Args:
            figsize: Figure size (width, height)
            save_path: Path to save the plot (if None, not saved)
            show_plot: Whether to display the plot
            x_label: Label for x-axis
            y_label: Label for y-axis
        """
        if self.baseline is None or self.detrended is None:
            raise ValueError("No baseline removal has been performed yet")

        fig, axes = plt.subplots(2, 2, figsize=figsize)
        fig.suptitle(f'Baseline Removal Results - Method: {self.method_used}', fontsize=14, fontweight='bold')

        # Plot 1: Original data with baseline
        axes[0, 0].plot(self.x_data, self.y_data, 'b-', alpha=0.7, label='Original Data', linewidth=1)
        axes[0, 0].plot(self.x_data, self.baseline, 'r-', label='Estimated Baseline', linewidth=2)

        # Highlight excluded peak region if applicable
        if self.ignore_deepest_peak and self.peak_mask is not None:
            peak_x = self.x_data[self.peak_mask]
            peak_y = self.y_data[self.peak_mask]
            axes[0, 0].fill_between(peak_x, np.min(self.y_data), np.max(self.y_data),
                                   alpha=0.2, color='orange', label='Excluded Peak Region')
            axes[0, 0].plot(peak_x, peak_y, 'o', color='orange', markersize=3, alpha=0.7)

        axes[0, 0].set_xlabel(x_label)
        axes[0, 0].set_ylabel(y_label)
        axes[0, 0].set_title('Original Data with Estimated Baseline')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Detrended signal
        axes[0, 1].plot(self.x_data, self.detrended, 'g-', linewidth=1)
        axes[0, 1].set_xlabel(x_label)
        axes[0, 1].set_ylabel(f'Detrended {y_label}')
        axes[0, 1].set_title('Detrended Signal (Fast Components)')
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: Comparison of original and detrended
        axes[1, 0].plot(self.x_data, self.y_data, 'b-', alpha=0.7, label='Original', linewidth=1)
        axes[1, 0].plot(self.x_data, self.detrended + np.mean(self.y_data), 'g-',
                       label='Detrended (offset)', linewidth=1)
        axes[1, 0].set_xlabel(x_label)
        axes[1, 0].set_ylabel(y_label)
        axes[1, 0].set_title('Original vs Detrended Comparison')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Signal statistics
        stats = self.get_signal_statistics()
        stats_text = f"Method: {self.method_used}\n"
        stats_text += f"Parameters: {self.method_params}\n\n"
        stats_text += f"Original STD: {stats['original_std']:.3f}\n"
        stats_text += f"Detrended STD: {stats['detrended_std']:.3f}\n"
        if 'snr_improvement' in stats:
            stats_text += f"SNR Improvement: {stats['snr_improvement']:.2f}x\n"
        stats_text += f"Baseline Range: {stats.get('baseline_range', 0):.3f}\n"
        stats_text += f"Detrended Range: {stats['detrended_range']:.3f}"

        axes[1, 1].text(0.05, 0.95, stats_text, transform=axes[1, 1].transAxes,
                       fontsize=10, verticalalignment='top', fontfamily='monospace',
                       bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].set_title('Processing Statistics')
        axes[1, 1].axis('off')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to: {save_path}")

        if show_plot:
            plt.show()
        else:
            plt.close()


def process_csv_data(csv_file: str, x_column: str = 'piezo_percentage',
                    y_column: str = 'power_dBm', method: str = 'auto',
                    save_results: bool = True, plot_results: bool = True,
                    ignore_deepest_peak: bool = False) -> Dict[str, Any]:
    """
    Process measurement data from CSV file with baseline removal.

    Args:
        csv_file: Path to CSV file
        x_column: Name of x-axis column (default: 'piezo_percentage')
        y_column: Name of y-axis column (default: 'power_dBm')
        method: Baseline removal method ('auto', 'polynomial', 'moving_average', etc.)
        save_results: Whether to save processed data to new CSV file
        plot_results: Whether to create and save plots
        ignore_deepest_peak: If True, excludes the deepest peak from baseline estimation

    Returns:
        Dictionary containing processed data and statistics
    """
    # Read data
    df = pd.read_csv(csv_file)

    # Check if specified columns exist, fallback to alternatives
    if x_column not in df.columns:
        if 'wavelength_nm' in df.columns:
            x_column = 'wavelength_nm'
            print(f"Using wavelength_nm as x-axis instead of {x_column}")
        else:
            raise ValueError(f"Column {x_column} not found in CSV file")

    if y_column not in df.columns:
        raise ValueError(f"Column {y_column} not found in CSV file")

    x_data = df[x_column].values
    y_data = df[y_column].values

    # Initialize baseline remover
    remover = BaselineRemover(x_data, y_data, ignore_deepest_peak=ignore_deepest_peak)

    # Apply baseline removal
    if method == 'auto':
        baseline, detrended = remover.auto_select_method()
    elif method == 'polynomial':
        baseline, detrended = remover.polynomial_detrend()
    elif method == 'moving_average':
        baseline, detrended = remover.moving_average_detrend()
    elif method == 'savitzky_golay':
        baseline, detrended = remover.savgol_detrend()
    elif method == 'highpass_filter':
        baseline, detrended = remover.highpass_filter_detrend()
    elif method == 'rolling_statistics':
        baseline, detrended = remover.rolling_statistics_detrend()
    elif method == 'adaptive_als':
        baseline, detrended = remover.adaptive_baseline_detrend()
    else:
        raise ValueError(f"Unknown method: {method}")

    # Get statistics
    stats = remover.get_signal_statistics()

    # Prepare results
    results = {
        'x_data': remover.x_data,
        'y_original': remover.y_data,
        'baseline': baseline,
        'detrended': detrended,
        'method': remover.method_used,
        'parameters': remover.method_params,
        'statistics': stats,
        'x_column': x_column,
        'y_column': y_column
    }

    # Save processed data
    if save_results:
        base_name = csv_file.replace('.csv', '')

        # Save processed data to new CSV
        processed_df = pd.DataFrame({
            x_column: remover.x_data,
            f'{y_column}_original': remover.y_data,
            f'{y_column}_baseline': baseline,
            f'{y_column}_detrended': detrended
        })

        processed_file = f"{base_name}_processed.csv"
        processed_df.to_csv(processed_file, index=False)
        print(f"Processed data saved to: {processed_file}")

        # Save statistics
        stats_file = f"{base_name}_stats.txt"
        with open(stats_file, 'w') as f:
            f.write(f"Baseline Removal Analysis\n")
            f.write(f"========================\n\n")
            f.write(f"Input file: {csv_file}\n")
            f.write(f"Method used: {remover.method_used}\n")
            f.write(f"Parameters: {remover.method_params}\n\n")
            f.write(f"Signal Statistics:\n")
            for key, value in stats.items():
                f.write(f"  {key}: {value:.6f}\n")

        print(f"Statistics saved to: {stats_file}")

    # Create plots
    if plot_results:
        plot_file = csv_file.replace('.csv', '_baseline_analysis.png')
        x_label = x_column.replace('_', ' ').title()
        y_label = y_column.replace('_', ' ').title()

        remover.plot_results(save_path=plot_file, show_plot=False,
                           x_label=x_label, y_label=y_label)

    return results


def compare_methods(x_data: np.ndarray, y_data: np.ndarray,
                   methods: Optional[list] = None,
                   save_path: Optional[str] = None,
                   show_plot: bool = True) -> Dict[str, Dict]:
    """
    Compare different baseline removal methods on the same data.

    Args:
        x_data: Independent variable data
        y_data: Dependent variable data
        methods: List of methods to compare (if None, uses default set)
        save_path: Path to save comparison plot
        show_plot: Whether to display the plot

    Returns:
        Dictionary containing results for each method
    """
    if methods is None:
        methods = ['polynomial', 'moving_average', 'savitzky_golay', 'rolling_statistics']

    results = {}

    # Process with each method
    for method in methods:
        try:
            remover = BaselineRemover(x_data, y_data)

            if method == 'polynomial':
                baseline, detrended = remover.polynomial_detrend()
            elif method == 'moving_average':
                baseline, detrended = remover.moving_average_detrend()
            elif method == 'savitzky_golay':
                baseline, detrended = remover.savgol_detrend()
            elif method == 'highpass_filter':
                baseline, detrended = remover.highpass_filter_detrend()
            elif method == 'rolling_statistics':
                baseline, detrended = remover.rolling_statistics_detrend()
            elif method == 'adaptive_als':
                baseline, detrended = remover.adaptive_baseline_detrend()

            results[method] = {
                'baseline': baseline,
                'detrended': detrended,
                'statistics': remover.get_signal_statistics(),
                'parameters': remover.method_params
            }

        except Exception as e:
            print(f"Method {method} failed: {str(e)}")
            continue

    # Create comparison plot
    if results:
        n_methods = len(results)
        fig, axes = plt.subplots(2, n_methods, figsize=(4*n_methods, 8))
        if n_methods == 1:
            axes = axes.reshape(2, 1)

        fig.suptitle('Baseline Removal Method Comparison', fontsize=14, fontweight='bold')

        for i, (method, result) in enumerate(results.items()):
            # Plot baselines
            axes[0, i].plot(x_data, y_data, 'b-', alpha=0.5, label='Original', linewidth=1)
            axes[0, i].plot(x_data, result['baseline'], 'r-', label='Baseline', linewidth=2)
            axes[0, i].set_title(f'{method.replace("_", " ").title()}')
            axes[0, i].legend()
            axes[0, i].grid(True, alpha=0.3)

            # Plot detrended signals
            axes[1, i].plot(x_data, result['detrended'], 'g-', linewidth=1)
            axes[1, i].set_title(f'Detrended ({method})')
            axes[1, i].grid(True, alpha=0.3)

            # Add statistics text
            stats = result['statistics']
            stats_text = f"STD: {stats['detrended_std']:.3f}\n"
            if 'snr_improvement' in stats:
                stats_text += f"SNR: {stats['snr_improvement']:.1f}x"

            axes[1, i].text(0.02, 0.98, stats_text, transform=axes[1, i].transAxes,
                           fontsize=8, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Comparison plot saved to: {save_path}")

        if show_plot:
            plt.show()
        else:
            plt.close()

    return results


def batch_process_folder(folder_path: str, pattern: str = "*.csv",
                        method: str = 'auto', x_column: str = 'piezo_percentage',
                        y_column: str = 'power_dBm') -> None:
    """
    Process all CSV files in a folder with baseline removal.

    Args:
        folder_path: Path to folder containing CSV files
        pattern: File pattern to match (default: "*.csv")
        method: Baseline removal method to use
        x_column: Name of x-axis column
        y_column: Name of y-axis column
    """
    import glob
    import os

    csv_files = glob.glob(os.path.join(folder_path, pattern))

    if not csv_files:
        print(f"No files found matching pattern {pattern} in {folder_path}")
        return

    print(f"Processing {len(csv_files)} files...")

    for csv_file in csv_files:
        try:
            print(f"\nProcessing: {os.path.basename(csv_file)}")
            results = process_csv_data(csv_file, x_column=x_column, y_column=y_column,
                                     method=method, save_results=True, plot_results=True)
            print(f"  Method used: {results['method']}")
            print(f"  SNR improvement: {results['statistics'].get('snr_improvement', 'N/A'):.2f}x")

        except Exception as e:
            print(f"  Error processing {csv_file}: {str(e)}")
            continue

    print(f"\nBatch processing complete!")


def create_synthetic_test_data(n_points: int = 400, noise_level: float = 0.1,
                              baseline_type: str = 'polynomial') -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Create synthetic test data with known baseline and signal components.

    Args:
        n_points: Number of data points
        noise_level: Noise level relative to signal
        baseline_type: Type of baseline ('polynomial', 'exponential', 'sinusoidal')

    Returns:
        Tuple of (x, y_total, y_baseline_true, y_signal_true)
    """
    x = np.linspace(0, 100, n_points)

    # Create true signal (fast-varying component)
    y_signal_true = (0.5 * np.sin(0.3 * x) +
                    0.3 * np.sin(0.8 * x) +
                    0.2 * np.sin(1.5 * x) +
                    0.1 * np.random.randn(n_points))

    # Create true baseline (slow-varying component)
    if baseline_type == 'polynomial':
        y_baseline_true = -5 + 0.02 * x - 0.0003 * x**2 + 0.000001 * x**3
    elif baseline_type == 'exponential':
        y_baseline_true = -5 + 2 * np.exp(-x/50)
    elif baseline_type == 'sinusoidal':
        y_baseline_true = -5 + 0.5 * np.sin(0.1 * x)
    else:
        y_baseline_true = -5 + 0.01 * x  # Linear

    # Add noise
    noise = noise_level * np.std(y_signal_true) * np.random.randn(n_points)

    # Combine components
    y_total = y_baseline_true + y_signal_true + noise

    return x, y_total, y_baseline_true, y_signal_true


def validate_method_performance(method: str = 'auto', n_tests: int = 10,
                               save_results: bool = True) -> Dict[str, float]:
    """
    Validate baseline removal method performance using synthetic data.

    Args:
        method: Method to test
        n_tests: Number of test cases to run
        save_results: Whether to save validation results

    Returns:
        Dictionary containing performance metrics
    """
    baseline_errors = []
    signal_errors = []
    snr_improvements = []

    for i in range(n_tests):
        # Generate test data
        baseline_type = np.random.choice(['polynomial', 'exponential', 'sinusoidal'])
        noise_level = np.random.uniform(0.05, 0.3)

        x, y_total, y_baseline_true, y_signal_true = create_synthetic_test_data(
            n_points=400, noise_level=noise_level, baseline_type=baseline_type)

        # Apply baseline removal
        remover = BaselineRemover(x, y_total)

        if method == 'auto':
            baseline_est, signal_est = remover.auto_select_method()
        elif method == 'polynomial':
            baseline_est, signal_est = remover.polynomial_detrend()
        elif method == 'moving_average':
            baseline_est, signal_est = remover.moving_average_detrend()
        elif method == 'savitzky_golay':
            baseline_est, signal_est = remover.savgol_detrend()
        else:
            continue

        # Calculate errors
        baseline_error = np.sqrt(np.mean((baseline_est - y_baseline_true)**2))
        signal_error = np.sqrt(np.mean((signal_est - y_signal_true)**2))

        baseline_errors.append(baseline_error)
        signal_errors.append(signal_error)

        # Calculate SNR improvement
        original_snr = np.std(y_signal_true) / np.std(y_total - y_signal_true)
        recovered_snr = np.std(y_signal_true) / np.std(signal_est - y_signal_true)
        snr_improvements.append(recovered_snr / original_snr)

    # Calculate performance metrics
    performance = {
        'mean_baseline_rmse': np.mean(baseline_errors),
        'std_baseline_rmse': np.std(baseline_errors),
        'mean_signal_rmse': np.mean(signal_errors),
        'std_signal_rmse': np.std(signal_errors),
        'mean_snr_improvement': np.mean(snr_improvements),
        'std_snr_improvement': np.std(snr_improvements),
        'success_rate': len(baseline_errors) / n_tests
    }

    if save_results:
        with open(f'validation_results_{method}.txt', 'w') as f:
            f.write(f"Baseline Removal Validation Results\n")
            f.write(f"===================================\n\n")
            f.write(f"Method: {method}\n")
            f.write(f"Number of tests: {n_tests}\n\n")
            f.write(f"Performance Metrics:\n")
            for key, value in performance.items():
                f.write(f"  {key}: {value:.6f}\n")

    return performance


# Example usage and testing functions
if __name__ == "__main__":
    # Create synthetic test data
    print("Creating synthetic test data...")
    x, y_total, y_baseline_true, y_signal_true = create_synthetic_test_data(
        n_points=400, noise_level=0.15, baseline_type='polynomial')

    # Test different methods
    print("\nTesting baseline removal methods...")

    # Method comparison
    results = compare_methods(x, y_total,
                            methods=['polynomial', 'moving_average', 'savitzky_golay', 'rolling_statistics'],
                            save_path='method_comparison.png', show_plot=False)

    # Individual method testing
    remover = BaselineRemover(x, y_total)

    print("\n1. Testing polynomial detrending...")
    baseline, detrended = remover.polynomial_detrend(degree=3)
    remover.plot_results(save_path='polynomial_test.png', show_plot=False)
    stats = remover.get_signal_statistics()
    print(f"   SNR improvement: {stats.get('snr_improvement', 'N/A'):.2f}x")

    print("\n2. Testing auto-selection...")
    remover2 = BaselineRemover(x, y_total)
    baseline, detrended = remover2.auto_select_method()
    remover2.plot_results(save_path='auto_selection_test.png', show_plot=False)

    print("\n3. Validation test...")
    performance = validate_method_performance(method='auto', n_tests=5, save_results=True)
    print(f"   Mean SNR improvement: {performance['mean_snr_improvement']:.2f}x")
    print(f"   Success rate: {performance['success_rate']:.1%}")

    print("\nAll tests completed! Check generated PNG files for results.")
