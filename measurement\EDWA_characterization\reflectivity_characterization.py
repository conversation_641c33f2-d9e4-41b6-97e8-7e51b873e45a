import traceback
import numpy as np
import time
import matplotlib.pyplot as plt
from tqdm import tqdm
from datetime import datetime
import pandas as pd
import os
import glob

from device_wrappers.Newfocus_TLB6700 import NewFocus_TLB6700_wrapper
from device_wrappers.Thorlabs_PMxxx.PMxxx_wrapper import PMxxx
from signal_processing import BaselineRemover, process_csv_data, compare_methods


class ReflectivityCharacterization:
    def __init__(self):
        self.start_wl = 1520
        self.end_wl = 1550
        self.fwd_scan_speed = 20.0
        self.bwd_scan_speed = 20.0
        self.laser_power = 3

        self.ecl = None
        self.pd = None

    def init(self):
        self.ecl = NewFocus_TLB6700_wrapper.NewFocus6700(id=4106, key='6700 SN70006')
        self.ecl.connect_laser(True)
        self.init_ecl()

        self.pd = PMxxx()
        self.init_pd()

    def init_ecl(self):
        # self.ecl.reset()
        # self.ecl.set_wl_scan_speed(wl_scan_speed_forward=self.fwd_scan_speed, wl_scan_speed_return=self.bwd_scan_speed)
        # self.ecl.set_wl_scan_limit(wl_scan_limit_value=[self.start_wl, self.end_wl])
        self.ecl.set_lbd(Lambda=1550)
        self.ecl.wait_for_tracking()
        self.ecl.set_pzt_voltage(0)
        self.ecl.set_out_power(power_mW=self.laser_power)

    def init_pd(self):
        self.pd.wavelength = 1550
        self.pd.auto_range = False
        self.pd.set_power_range(value_W=3.9e-3)  # TODO: check the power range
        self.pd.set_average(3)

    def cal_scan_time(self):
        # wl_lims  = self.ecl.query_wl_scan_limit()
        # print(f"Wavelength limits: {wl_lims}")    # empty
        wl_span = self.end_wl - self.start_wl
        # frd_speed, bwd_speed = self.ecl.query_scan_speed()    # empty
        # scan_config = self.ecl.get_scan_config()
        # print(f"Scan config: {scan_config}")
        scan_time = wl_span / self.fwd_scan_speed
        if self.bwd_scan_speed != 0:
            scan_time += wl_span / self.bwd_scan_speed

        return scan_time

    def start_sweeping(self):
        scan_time = self.cal_scan_time()
        print(f"Scan time: {scan_time}")

        self.ecl.set_output_state(value=True)
        self.ecl.start_wl_scan_process(True)  # the scan always go forth and back. Back scan is faster than forward.
        time.sleep(scan_time + 3)

    def measure_pt_by_pt(self):
        self.ecl.set_output_state(value=True)
        wls = np.linspace(self.start_wl, self.end_wl, 10)
        pws = np.zeros_like(wls)
        for i, wl in enumerate(tqdm(wls, desc="Sweeping wavelengths", unit="wavelengths")):
            self.ecl.set_lbd(Lambda=wl)
            self.pd.wavelength = wl
            self.ecl.wait_for_tracking()
            # while not self.ecl.is_operation_complete():
            # time.sleep(0.1)
            pws[i] = self.pd.get_power()
        return wls, pws

    def measure_pt_by_pt_piezo(self):
        self.ecl.set_output_state(value=True)
        self.ecl.set_track_mode(False)

        piezo_pcts = np.linspace(0, 100, 400)
        pws = np.zeros_like(piezo_pcts)
        wls = np.zeros_like(piezo_pcts)

        for i, piezo_pct in enumerate(tqdm(piezo_pcts, desc="Sweeping with piezo")):
            self.ecl.set_pzt_voltage(piezo_pct)
            time.sleep(1)
            # while not self.ecl.is_operation_complete():
            # time.sleep(0.1)

            wls[i] = float(self.ecl.query_lbd())
            self.pd.wavelength = wls[i]
            # time.sleep(0.2)
            pws[i] = self.pd.get_power()
        return piezo_pcts, wls, pws

    def save_data_to_csv(self, piezo_pcts, wavelengths, powers, filename=None):
        """
        Save measurement data to CSV file with headers.

        Args:
            piezo_pcts: Array of piezo percentages (can be None for wavelength-only sweeps)
            wavelengths: Array of wavelengths (nm)
            powers: Array of power measurements (dBm)
            filename: Optional filename. If None, generates timestamp-based name

        Returns:
            str: The filename of the saved file
        """
        # Filter out NaN values and invalid readings
        if piezo_pcts is not None:
            valid_mask = ~(np.isnan(wavelengths) | np.isnan(powers) | np.isnan(piezo_pcts))
        else:
            valid_mask = ~(np.isnan(wavelengths) | np.isnan(powers))

        if not np.any(valid_mask):
            print("Warning: No valid data points to save!")
            return None

        # Create DataFrame with valid data only
        data = {
            'wavelength_nm': wavelengths[valid_mask],
            'power_dBm': powers[valid_mask]
        }

        # Add piezo data if available
        if piezo_pcts is not None:
            data['piezo_percentage'] = piezo_pcts[valid_mask]

        df = pd.DataFrame(data)

        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'reflectivity_data_{timestamp}.csv'

        # Ensure filename has .csv extension
        if not filename.endswith('.csv'):
            filename += '.csv'

        # Save to CSV
        df.to_csv(filename, index=False)
        print(f"Data saved to {filename} ({len(df)} valid data points)")

        return filename

    @staticmethod
    def read_data_from_csv(filename):
        """
        Read measurement data from CSV file.

        Args:
            filename: Path to the CSV file

        Returns:
            tuple: (piezo_pcts, wavelengths, powers) arrays
                   piezo_pcts will be None if not present in the file
        """
        if not os.path.exists(filename):
            raise FileNotFoundError(f"File {filename} not found")

        df = pd.read_csv(filename)

        # Validate required columns (wavelength and power are always required)
        required_columns = ['wavelength_nm', 'power_dBm']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        wavelengths = df['wavelength_nm'].values
        powers = df['power_dBm'].values

        # Check if piezo data is available
        if 'piezo_percentage' in df.columns:
            piezo_pcts = df['piezo_percentage'].values
        else:
            piezo_pcts = None

        print(f"Loaded {len(df)} data points from {filename}")
        if piezo_pcts is not None:
            print("Data includes piezo percentage information")
        else:
            print("Data contains wavelength and power only")

        return piezo_pcts, wavelengths, powers

    def apply_baseline_removal(self, piezo_pcts: np.ndarray, wavelengths: np.ndarray,
                               powers: np.ndarray, method: str = 'auto',
                               x_axis: str = 'piezo', ignore_deepest_peak: bool = False) -> dict:
        """
        Apply baseline removal to measurement data.

        Args:
            piezo_pcts: Array of piezo percentages (can be None)
            wavelengths: Array of wavelengths
            powers: Array of power measurements
            method: Baseline removal method ('auto', 'polynomial', 'moving_average', etc.)
            x_axis: Which axis to use for baseline removal ('piezo', 'wavelength')
            ignore_deepest_peak: If True, excludes the deepest peak from baseline estimation

        Returns:
            Dictionary containing original data, baseline, and detrended signal
        """
        # Choose x-axis data
        if x_axis == 'piezo' and piezo_pcts is not None:
            x_data = piezo_pcts
            x_label = 'Piezo Percentage (%)'
        else:
            x_data = wavelengths
            x_label = 'Wavelength (nm)'

        # Initialize baseline remover
        remover = BaselineRemover(x_data, powers, ignore_deepest_peak=ignore_deepest_peak)

        # Apply selected method
        if method == 'auto':
            baseline, detrended = remover.auto_select_method()
        elif method == 'polynomial':
            baseline, detrended = remover.polynomial_detrend()
        elif method == 'moving_average':
            baseline, detrended = remover.moving_average_detrend()
        elif method == 'savitzky_golay':
            baseline, detrended = remover.savgol_detrend()
        elif method == 'highpass_filter':
            baseline, detrended = remover.highpass_filter_detrend()
        elif method == 'rolling_statistics':
            baseline, detrended = remover.rolling_statistics_detrend()
        elif method == 'adaptive_als':
            baseline, detrended = remover.adaptive_baseline_detrend()
        else:
            raise ValueError(f"Unknown baseline removal method: {method}")

        # Get statistics
        stats = remover.get_signal_statistics()

        return {
            'x_data': x_data,
            'x_label': x_label,
            'y_original': powers,
            'baseline': baseline,
            'detrended': detrended,
            'method': remover.method_used,
            'parameters': remover.method_params,
            'statistics': stats,
            'remover': remover  # Keep remover object for plotting
        }

    def save_processed_data_to_csv(self, piezo_pcts: np.ndarray, wavelengths: np.ndarray,
                                   powers: np.ndarray, baseline_results: dict,
                                   filename: str = None) -> str:
        """
        Save original and processed data to CSV file.

        Args:
            piezo_pcts: Array of piezo percentages (can be None)
            wavelengths: Array of wavelengths
            powers: Array of power measurements
            baseline_results: Results from apply_baseline_removal()
            filename: Optional filename

        Returns:
            str: The filename of the saved file
        """
        # Create DataFrame with all data
        data = {
            'wavelength_nm': wavelengths,
            'power_dBm_original': powers,
            'power_dBm_baseline': baseline_results['baseline'],
            'power_dBm_detrended': baseline_results['detrended']
        }

        # Add piezo data if available
        if piezo_pcts is not None:
            data['piezo_percentage'] = piezo_pcts

        df = pd.DataFrame(data)

        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            method = baseline_results['method']
            filename = f'reflectivity_processed_{method}_{timestamp}.csv'

        # Ensure filename has .csv extension
        if not filename.endswith('.csv'):
            filename += '.csv'

        # Save to CSV
        df.to_csv(filename, index=False)
        print(f"Processed data saved to {filename} ({len(df)} data points)")

        # Save processing metadata
        metadata_file = filename.replace('.csv', '_metadata.txt')
        with open(metadata_file, 'w') as f:
            f.write(f"Baseline Removal Processing Metadata\n")
            f.write(f"====================================\n\n")
            f.write(f"Processing timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Method used: {baseline_results['method']}\n")
            f.write(f"Parameters: {baseline_results['parameters']}\n")
            f.write(f"X-axis: {baseline_results['x_label']}\n\n")
            f.write(f"Signal Statistics:\n")
            for key, value in baseline_results['statistics'].items():
                f.write(f"  {key}: {value:.6f}\n")

        print(f"Processing metadata saved to {metadata_file}")

        return filename

    def close(self):
        if self.ecl is not None:
            self.ecl.set_output_state(value=False)
        if self.pd is not None:
            self.pd.close()


def plot_folder(folder_path):
    csv_files = glob.glob(os.path.join(folder_path, '*.csv'), recursive=False)
    for csv_file in csv_files:
        plot_measurement(csv_file, save_plot=True, show_plot=False)


def plot_measurement(csv_file, save_plot=False, show_plot=True):
    piezo_pcts, wavelengths, powers = ReflectivityCharacterization.read_data_from_csv(csv_file)

    # Create two subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10))

    # Subplot 1: Power vs Wavelength
    ax1.plot(wavelengths, powers, '.-', markersize=3)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('Wavelength (nm)')
    ax1.set_ylabel('Power (dBm)')
    ax1.set_title(f'Power vs Wavelength')

    # Subplot 2: Power vs Piezo Percentage (only if piezo data is available)
    if piezo_pcts is not None:
        ax2.plot(piezo_pcts, powers, '.-', markersize=3)
        ax2.grid(True, alpha=0.3)
        ax2.set_xlabel('Piezo Percentage (%)')
        ax2.set_ylabel('Power (dBm)')
        ax2.set_title(f' Power vs Piezo Percentage')
    else:
        ax2.text(0.5, 0.5, 'No piezo data available',
                 horizontalalignment='center', verticalalignment='center',
                 transform=ax2.transAxes, fontsize=12)
        ax2.set_title(f'Power vs Piezo Percentage (No Data)')

    plt.tight_layout()
    if save_plot:
        plt.savefig(csv_file[:-len('.csv')] + '.png', dpi=300, bbox_inches='tight')
    if show_plot:
        plt.show()
    plt.close()


def plot_measurement_with_baseline_removal(csv_file, method='auto', x_axis='piezo',
                                           save_plot=True, show_plot=True, ignore_deepest_peak=False):
    """
    Plot measurement data with baseline removal analysis.

    Args:
        csv_file: Path to CSV file
        method: Baseline removal method ('auto', 'polynomial', etc.)
        x_axis: Which axis to use for analysis ('piezo', 'wavelength')
        save_plot: Whether to save the plot
        show_plot: Whether to display the plot
        ignore_deepest_peak: If True, excludes the deepest peak from baseline estimation
    """
    # Load data
    piezo_pcts, wavelengths, powers = ReflectivityCharacterization.read_data_from_csv(csv_file)

    # Create ReflectivityCharacterization instance for processing
    ref_char = ReflectivityCharacterization()

    # Apply baseline removal
    try:
        baseline_results = ref_char.apply_baseline_removal(
            piezo_pcts, wavelengths, powers, method=method, x_axis=x_axis,
            ignore_deepest_peak=ignore_deepest_peak)

        # Create comprehensive plot
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'Baseline Removal Analysis - {os.path.basename(csv_file)}',
                     fontsize=14, fontweight='bold')

        x_data = baseline_results['x_data']
        x_label = baseline_results['x_label']

        # Plot 1: Original data with baseline
        axes[0, 0].plot(x_data, powers, 'b-', alpha=0.7, label='Original Data', linewidth=1)
        axes[0, 0].plot(x_data, baseline_results['baseline'], 'r-',
                        label='Estimated Baseline', linewidth=2)
        axes[0, 0].set_xlabel(x_label)
        axes[0, 0].set_ylabel('Power (dBm)')
        axes[0, 0].set_title('Original Data with Estimated Baseline')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Detrended signal
        axes[0, 1].plot(x_data, baseline_results['detrended'], 'g-', linewidth=1)
        axes[0, 1].set_xlabel(x_label)
        axes[0, 1].set_ylabel('Detrended Power (dBm)')
        axes[0, 1].set_title('Detrended Signal (Fast Components)')
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: Comparison view
        axes[1, 0].plot(x_data, powers, 'b-', alpha=0.7, label='Original', linewidth=1)
        axes[1, 0].plot(x_data, baseline_results['detrended'] + np.mean(powers), 'g-',
                        label='Detrended (offset)', linewidth=1)
        axes[1, 0].set_xlabel(x_label)
        axes[1, 0].set_ylabel('Power (dBm)')
        axes[1, 0].set_title('Original vs Detrended Comparison')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Statistics and info
        stats = baseline_results['statistics']
        stats_text = f"Method: {baseline_results['method']}\n"
        stats_text += f"Parameters: {baseline_results['parameters']}\n\n"
        stats_text += f"Original STD: {stats['original_std']:.3f}\n"
        stats_text += f"Detrended STD: {stats['detrended_std']:.3f}\n"
        if 'snr_improvement' in stats:
            stats_text += f"SNR Improvement: {stats['snr_improvement']:.2f}x\n"
        stats_text += f"Baseline Range: {stats.get('baseline_range', 0):.3f}\n"
        stats_text += f"Detrended Range: {stats['detrended_range']:.3f}"

        axes[1, 1].text(0.05, 0.95, stats_text, transform=axes[1, 1].transAxes,
                        fontsize=10, verticalalignment='top', fontfamily='monospace',
                        bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].set_title('Processing Statistics')
        axes[1, 1].axis('off')

        plt.tight_layout()

        if save_plot:
            plot_filename = csv_file.replace('.csv', f'_baseline_{method}.png')
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            print(f"Baseline analysis plot saved to: {plot_filename}")

        if show_plot:
            plt.show()
        else:
            plt.close()

        # Save processed data
        processed_filename = ref_char.save_processed_data_to_csv(
            piezo_pcts, wavelengths, powers, baseline_results)

        return baseline_results

    except Exception as e:
        print(f"Error in baseline removal analysis: {str(e)}")
        # Fall back to regular plotting
        plot_measurement(csv_file, save_plot=save_plot, show_plot=show_plot)
        return None


if __name__ == '__main__':
    # reflectivity_char = ReflectivityCharacterization()
    # # reflectivity_char.init()
    # try:
    #     filename = f'reflectivity_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    #     # Option 1: Point-by-point wavelength sweep
    #     # wls, pws = reflectivity_char.measure_pt_by_pt()
    #     # csv_filename = reflectivity_char.save_wl_power_to_csv(wls, pws)

    #     # Option 2: Piezo sweep (current default)
    #     # piezo_p, wls, pws = reflectivity_char.measure_pt_by_pt_piezo()

    #     # Save data to CSV (includes piezo data)
    #     # csv_filename = reflectivity_char.save_data_to_csv(piezo_p, wls, pws, filename='lensed_fibers_ref3_wait0p5s.csv')

    #     plot_measurement(f'{filename}.csv', save_plot=True, show_plot=True)

    # except:
    #     print(traceback.format_exc())
    # finally:
    #     reflectivity_char.close()

    # Example 1: Regular plotting of all files in folder
    # plot_folder(r'C:\Users\<USER>\Documents\EDWA_setup\reflection_measurement')

    # Example 2: Baseline removal analysis on specific file
    test_file = r'C:\Users\<USER>\Documents\EDWA_setup\reflection_measurement - Copy\01_ring_20250716_162748.csv'
    if os.path.exists(test_file):
        print(f"Analyzing {test_file} with baseline removal...")

        # Standard baseline removal
        print("1. Standard baseline removal (including all data):")
        results1 = plot_measurement_with_baseline_removal(
            test_file, method='auto', x_axis='piezo', save_plot=True, show_plot=False,
            ignore_deepest_peak=False)
        if results1:
            print(f"   SNR improvement: {results1['statistics'].get('snr_improvement', 'N/A'):.2f}x")
            print(f"   Method used: {results1['method']}")

        # Baseline removal with peak exclusion
        print("\n2. Baseline removal (excluding deepest peak):")
        results2 = plot_measurement_with_baseline_removal(
            test_file, method='auto', x_axis='piezo', save_plot=True, show_plot=True,
            ignore_deepest_peak=True)
        if results2:
            print(f"   SNR improvement: {results2['statistics'].get('snr_improvement', 'N/A'):.2f}x")
            print(f"   Method used: {results2['method']}")
            excluded_points = results2['parameters'].get('excluded_peak_points', 0)
            print(f"   Excluded peak points: {excluded_points}")

            # Compare improvements
            if results1 and results2:
                snr1 = results1['statistics'].get('snr_improvement', 1)
                snr2 = results2['statistics'].get('snr_improvement', 1)
                if excluded_points > 0:
                    print(f"   🎉 Peak exclusion benefit: {snr2 / snr1:.2f}x better SNR!")
                else:
                    print(f"   ℹ️  No significant peak detected for exclusion")
    else:
        print(f"Test file not found: {test_file}")
        print("Please update the test_file path to point to your CSV file.")

    # Example 3: Batch processing with baseline removal
    # from .signal_processing import batch_process_folder
    # batch_process_folder(r'C:\Users\<USER>\Documents\EDWA_setup\reflection_measurement',
    #                     method='auto', x_column='piezo_percentage', y_column='power_dBm')

    print("\n🎉 Enhanced baseline removal functionality added to ReflectivityCharacterization!")
    print("📖 Usage examples:")
    print("   # Standard baseline removal:")
    print("   plot_measurement_with_baseline_removal('file.csv', ignore_deepest_peak=False)")
    print("   # With peak exclusion (ignores deepest peak):")
    print("   plot_measurement_with_baseline_removal('file.csv', ignore_deepest_peak=True)")
    print("\n🔬 Peak exclusion is perfect for data with deep resonance peaks!")
    print("   It provides better baseline trend estimation by excluding sharp features.")
