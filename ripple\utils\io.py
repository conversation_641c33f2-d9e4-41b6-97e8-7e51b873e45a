import pickle
import os
import numpy as np


def create_file(file_path):
    if os.path.exists(file_path):
        while True:
            overwrite = input("Overwrite? [y/n]")
            if overwrite.lower() in ['y', 'yes']:
                break
            elif overwrite.lower() in ['n', 'no']:
                return
    basedir = os.path.dirname(file_path)
    if not os.path.exists(basedir):
        os.makedirs(basedir)
    open(file_path, 'w').close()


def list_files_in_dir(dir_path: str, suffixes: str or list = None) -> list:
    """Return the filename (without the full path) inside the given folder"""
    file_list = [f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))]
    if isinstance(suffixes, str):
        suffixes = [suffixes]

    if isinstance(suffixes, (list, np.ndarray)):
        return [tf for tf in file_list if any(tf[-len(suf):] == suf for suf in suffixes)]
    else:
        return [tf for tf in file_list]


def append_to_file(file_path, content):
    with open(file_path, 'a') as file:
        file.write(content + '\n')


def print_log_file(file_path):
    with open(file_path, 'r') as file:
        content = file.read()
    print(content)


def safe_to_write(file_path):
    over_write = True
    if os.path.exists(file_path):
        while True:
            over_write = input(f'File {file_path} exists. Overwrite? [y/n] ')
            if over_write.lower() in ['yes', 'y']:
                break
            elif over_write.lower() in ['no', 'n']:
                over_write = False
                break
    return over_write


def save_field(field_type, x, y, field, path):
    data_dict = {'field_type': field_type, 'x': x, 'y': y, 'field': field}
    if safe_to_write(path):
        assert path.endswith('.wpmf'), 'Unknown file type.'
        with open(path, 'wb') as file:
            pickle.dump(data_dict, file)
        print(f'Saved data to {path}')


def read_field(path):
    assert path.endswith('.wpmf'), 'Unknown file type.'
    with open(path, 'rb') as file:
        data_dict = pickle.load(file)
    return data_dict['field_type'], data_dict['x'], data_dict['y'], data_dict['field']


if __name__ == "__main__":
    test_file = '../scripts/data/Optimization_log_2024-03-20_15-51-05.txt'
    create_file(test_file)
    append_to_file(test_file, 'Hallo')
    append_to_file(test_file, 'Hey')
    print_log_file(test_file)



