import pyvisa
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from .file_handling import file_accessible
import logging
import traceback
import time


pyvisa.logger.setLevel(logging.DEBUG)
visa_debug_on = False
if visa_debug_on:
    # pyvisa.logger.setLevel(logging.DEBUG)
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    ch.setFormatter(formatter)
    pyvisa.logger.addHandler(ch)

trace_dict = {0: 'A', 1: 'B', 2: 'C'}


class ANDO_AQ6317B(object):
    """
    Hardware wrapper for ANDO AQ6317B Optical Spectrum Analyzer (OSA)
    Reference: AQ6317B OPTICAL SPECTRUM ANALYZER INSTRUCTION MANUAL
    If any desired method is missing, <NAME_EMAIL>.
    """

    def __init__(self, visa_search_term='GPIB0::5'):
        self.osa_rm = pyvisa.ResourceManager()
        # print("All resources:", self.osa_rm.list_resources())
        try:
            self.inst = self.osa_rm.open_resource(visa_search_term)
            print(f"[INFO] Connected to ANDO AQ6317B OSA at {visa_search_term}")
        except Exception as e:
            raise Exception(f"Failed to connect to OSA at {visa_search_term}. Error: {e}")
        self.inst.write_termination = ''
        self.inst.read_termination = ''
        # self.inst.read_termination = '\r\n'
        del self.inst.timeout       # set an infinite timeout
        # self.set_timeout(25000)
        self.min_wl = 0
        self.max_wl = 2350
        self.meas_with_header = False
        self.header_on(True)
        self.set_output_digit(3)

        self._trace = 'TRA'

    def identify(self):
        """
        Returns:
            str: The response from an ``*IDN?`` GPIB query.
        """
        return self.inst.query(u'*IDN?')

    # SWEEP #
    def auto_sweep(self):
        """Starts an auto sweep."""
        self.inst.write(u'AUTO')

    def repeat_sweep(self):
        """Starts a repeat sweep."""
        self.inst.write(u'RPT')

    def single_sweep(self):
        """Starts a single sweep."""
        self.inst.write(u'SGL')

    def stop_sweep(self):
        """Stops a sweep."""
        self.inst.write(u'STP')

    def get_sweep_info(self):
        """Gets measurement status."""
        status_dict = {0: 'STOP', 1: 'SINGLE', 2: 'REPEAT', 3: 'AUTO', 4: 'SEGMENT MEASURE',
                       11: 'WL CAL', 12: 'OPTICAL ALIGNMENT'}
        return status_dict[self.inst.query(u'SWEEP?')]

    def set_sweep_marker(self, enable):
        """
        Selects ON or OFF for the marker-to-marker sweep function.

        Parameters:
            enable: int
                ON: 1, OFF: 0
        """
        self.inst.write(u'SWPM%i' % enable)

    def set_sweep_interval(self, period):
        """
        Sets the time from a start of a sweep till a start of the next sweep in the repeat sweep mode.

        Parameters:
            period: int
                from 0 to 99999 (1 step). (Unit: sec)
        """
        self.inst.write(u'SWPI%i' % period)

    # RANGE #
    def set_wl_range(self, wl_start, wl_stop):
        """
        Sets the wavelength range.

        Parameters:
             wl_start: float
                the measurement start wavelength from 0.00 to 1750.00 (0.01 step) in nm.
             wl_stop: float
                the measurement stop wavelength from 600.00 to 2350.00 (0.01 step) in nm.
        """
        assert (wl_start >= 0.) and (wl_start <= 1750.00)
        assert (wl_stop >= 600.00) and (wl_stop <= 2350.00)
        self.inst.write(u'STAWL%.2f' % wl_start)
        self.inst.write(u'STPWL%.2f' % wl_stop)
        self.min_wl = wl_start
        self.max_wl = wl_stop

    def set_wl_span(self, wl_center, wl_span):
        """
        Sets the wavelength range.

        Parameters:
            wl_center: float
                the center wavelength from 600.00 to 1750.00 (0.01 step) in nm.
            wl_span: float
                the span from 0.5 to 1200.0 (0.1 step) or 0 in nm.
            """
        wl_center = round(float(wl_center), 2)
        wl_span = round(float(wl_span), 1)
        assert (wl_center >= 600.00) and (wl_center <= 1750.00)
        assert (wl_span >= 0.5) and (wl_span <= 1200.0)
        self.inst.write(u'CTRWL%.2f' % wl_center)
        self.inst.write(u'SPAN%.1f' % wl_span)
        self.min_wl = round(wl_center - wl_span / 2., 2)
        self.max_wl = round(wl_center + wl_span / 2., 2)

    def set_fq_range(self, fq_start, fq_stop):
        """
        Sets the frequency range.

        Parameters:
            fq_start: float
                measurement start frequency from  1.000 to 499.500 (0.001 step) in THz
            fq_stop: float
                measurement end frequency from 171.500 to 674.500 (0.001 step) in THz
        """
        assert (fq_start >= 1.000) and (fq_start <= 499.500)
        assert (fq_stop >= 171.500) and (fq_stop <= 674.500)
        self.inst.write(u'STAF' + str(fq_start))
        self.inst.write(u'STPF' + str(fq_stop))
        self.min_wl, self.max_wl = self.get_wl_range()

    def set_fq_span(self, fq_center, fq_span):
        """
        Set the frequency range.

        Parameters:
            fq_center: float
                center frequency from 171.500 to 499.500 (0.001 step) in THz.
            fq_span: float
                the span from 0.100 to 350.000 (0.001 step) or 0 in THz
        """
        assert (fq_center >= 171.500) and (fq_center <= 499.500)
        assert (fq_span >= 0.100) and (fq_span <= 350.000)
        self.inst.write(u'CTRF%.3f' % fq_center)
        self.inst.write(u'SPANF%.3f' % fq_span)

    def get_wl_range(self):
        """Returns the wavelength range as a list."""
        wl_start = str(self.inst.query(u'STAWL?'))
        wl_stop = str(self.inst.query(u'STPWL?'))
        str_wl = wl_start + wl_stop
        return [float(x) for x in list(str_wl.split('\r\n'))[:-1]]

    # LEVEL #
    def set_ref_level_dbm(self, ref_level):
        """Sets the reference level. [in LOG] (Unit: dBm)"""
        assert isinstance(ref_level, (int, float))
        assert ((ref_level >= -90) and (ref_level <= 20))
        self.inst.write(u'REFL%.1f' % ref_level)

    def set_level_scale(self, scale):
        """
        Sets the scale of the level axis.

        Parameters:
            scale: float
                from 0.1 to 10.0 (0.1 step. Unit: dB/DIV) or LIN (linear scale)
        """
        if isinstance(scale, (float, int)) & (scale in np.arange(0.1, 10.1, 0.1)):
            self.inst.write(u'LSCL%.1f' % scale)
            print("Set the scale of the level axis to {} dB/DIV.".format(scale))
        elif scale == 'LIN':
            self.inst.write(u'LSCLLIN')
            print("Set the scale of the level axis to linear scale.")
        else:
            raise ValueError("set_level_scale: invalid input.")

    def auto_scaling(self, enable):
        """
        Selects ON or OFF for the auto scaling display function

        Parameters:
            enable: int
                ON: 1 OFF: 0
        """
        if enable in [0, 1]:
            self.inst.write(u'ATSCL%i' % enable)
            print("Auto subscale.")
        else:
            raise ValueError("auto_scaling: invalid input.")

    # SETUP #
    def set_average_times(self, avg):
        """
        Sets the number of averaging times for measurement.

        Parameters:
            avg: int
                from 1 to 1000 (1 step)
        """
        assert isinstance(avg, int)
        assert (avg >= 1) and (avg <= 1000)
        self.inst.write(u'AVG%i' % avg)

    def set_sampling_point(self, nb_points):
        """
        Sets the sampling point for measurement.

        Parameters:
            nb_points: int
                from 11 to 20001 (1 step), 0 (auto)
                (from experience, auto mode (0), does not work for single sweep)
        """
        assert isinstance(nb_points, int)
        assert ((nb_points >= 11) & (nb_points <= 20001) or nb_points == 0)
        self.inst.write(u'SMPL%i' % nb_points)

    def get_sampling_point(self):
        """Get the number of sampling point."""
        return int(self.inst.query(u'SMPL?'))

    def set_wl_resolution(self, wl_resln):
        """
        Sets the resolution.

        Parameters:
            wl_resln: float
                from 0.01 to 2.0 (1-2-5 steps). (Unit: nm)
        """
        assert (wl_resln >= 0.01) & (wl_resln <= 2.0)
        # self.inst.write(u'RESLN' + str(wl_resln))
        self.inst.write(u'RESLN%.2f' % wl_resln)

    def set_fq_resolution(self, fq_resln):
        """
         Sets the resolution.

         Parameters:
            fq_resln: int
                value in [2, 4, 10, 20, 40, 100, 200, 400]. (Unit: GHz)
        """
        resln_list = [2, 4, 10, 20, 40, 100, 200, 400]
        if fq_resln not in resln_list:
            raise ValueError("The input fq resolution should be in range: {}".format(resln_list))
        else:
            self.inst.write(u'RESLNF%i' % fq_resln)
            print("Set frequency resolution to {} GHz".format(fq_resln))

    def get_resolution(self):
        """Gets the resolution.

        Returns:
            wl_res: float
                resolution in wavelength domain. (Unit: nm)
            fq_res: float
                resolution in frequency domain. (Unit: GHz)
        """
        wl_res = float(self.inst.query(u'RESLN?'))
        fq_res = float(self.inst.query(u'RESLNF?'))
        return wl_res, fq_res

    def set_sensitivity(self, level):
        """
        Sets the measuring sensitivity.

        Parameters:
            level: str
                value from ['NORM RANGE HOLD', 'NORM RANGE AUTO', 'MID', 'HIGH1', 'HIGH2', 'HIGH3']
        """
        sens_lvl = {'NORM RANGE HOLD': u'SNHD', 'NORM RANGE AUTO': u'SNAT', 'MID': u'SMID',
                    'HIGH1': u'SHI1', 'HIGH2': u'SHI2', 'HIGH3': u'SHI3'}
        level = level.upper()
        assert level in sens_lvl
        self.inst.write(sens_lvl[level])
        # print('Set sensitivity to {}'.format(level))

    def get_sensitivity(self):
        lvl_dict = {'NORM RANGE HOLD': 4, 'NORM RANGE AUTO': 5, 'MID': 6, 'HIGH1': 1, 'HIGH2': 2, 'HIGH3': 3}
        level = self.inst.query(u'SENS?')
        return lvl_dict.keys()[lvl_dict.values().index(level)]

    def change_x_scale_unit(self, unit):
        """
        Switches the display of the X-axis scale to the wavelength or frequency.

        Parameters:
            unit: str
                value in ['wl', 'fq']
        """
        units = {'wl': 0, 'fq': 1}
        assert unit in units.keys()
        self.inst.write(u'XUNT%i' % units[unit])
        print("Changed the x-axis scale to {}.".format('wavelength' if unit == 'wl' else 'frequency'))

    def sync_to_tls(self, enable):
        """
        Selects ON or OFF for the TLS synchronization sweep function.

        Parameters:
            enable: int
                ON: 1; OFF: 0.
        """
        assert enable in [0, 1], "ValueError: input can only be 0 or 1."
        self.inst.write(u'TLSSYNC%i' % enable)

    # MARKER #
    def set_line_marker_wl(self, mrk_num, val):
        """
        Sets the wavelength line marker 1.

        Parameters:
            mrk_num: int
                1 or 2, the number of marker
            val: float
                from 0.000 to 2350.000 (Unit: nm)
        """
        assert mrk_num in [1, 2]
        assert (val >= 0) & (val <= 2350)
        # self.inst.write('L' + str(mrk_num) + 'MK' + str(val))
        self.inst.write(u'L%iMK%.3f' % (mrk_num, val))

    def set_line_marker_fq(self, mrk_num, val):
        """
        Sets the wavelength line marker 1.

        Parameters:
            mrk_num: int
                1 or 2, the number of marker
            val: float
                1.0000 to 674.5000 (Unit: THz)
        """
        assert mrk_num in [1, 2]
        assert (val >= 1) & (val <= 674.5)
        # self.inst.write('L' + str(mrk_num) + 'FMK' + str(val))
        self.inst.write(u'L%iFMK%.4f' % (mrk_num, val))

    def clear_line_marker(self):
        """Clears the line markers and line marker values."""
        self.inst.write(u'LMKCL')

    def clear_all_markers(self):
        """Clears the moving marker, fixed markers and the marker values in the data area."""
        self.inst.write(u'MKCL')
        self.clear_line_marker()

    # PEAK SEARCH #
    def search_peak(self):
        """Detects the MAX value of level."""
        self.inst.write(u'PKSR')

    def search_bottom(self):
        """Detects the MIN value of level."""
        self.inst.write(u'BTSR')

    def search_next_extreme(self):
        """Detects the next largest value (or next smallest value)"""
        self.inst.write(u'NSR')

    def search_next_right(self):
        """Detects the largest value (or smallest value) on the right side of the moving marker"""
        self.inst.write(u'NSRR')

    def search_next_left(self):
        """Detects the largest value (or smallest value) on the left side of the moving marker."""
        self.inst.write(u'NSRL')

    # ANALYSIS #
    def measure_spec_width(self, algorithm, threshold):
        """
        Measures the spectral bandwidth with the specified algorithm .
            algorithm: str
                ENVELOPE: 0, THRESH *: 1, RMS *: 2,
                PEAK RMS *: 3, NOTCH *: 4
            threshold: float
                Measures the spectrum width in Envelope method. **.**: 0.01 to 50.00 (0.01 step)
        """
        assert isinstance(algorithm, str)
        assert (threshold >= 0.01) and (threshold <= 50.00)
        algorithm = algorithm.upper()
        algorithms = {'ENVELOPE': 0, 'THRESH': 1, 'RMS': 2, 'PEAK RMS': 3, 'NOTCH': 4}
        assert algorithm in algorithms, 'ValuesError: undefined option.'

        self.inst.write(u'SW%i' % algorithms[algorithm])
        if algorithm == 'ENVELOPE':
            self.inst.write(u'SWENV%.2f' % threshold)

    # TRACE #
    def display_trace(self, trace, enable):
        """
        Selects display or non-display for trace A, B or C.

        Parameters:
            trace: str
                in ['A', 'B', 'C']
            enable: boolean
                Ture/False to enable/disable display
        """
        assert trace.upper() in ['A', 'B', 'C']
        if enable:
            self.inst.write(u'DSP%s' % trace.upper())
        else:
            self.inst.write(u'BLK%s' % trace.upper())

    def get_active_trace(self):
        active_trace = int(self.inst.query(u'ACTV?')[0])
        return trace_dict[active_trace]

    def active_trace(self, trace):
        assert trace.upper() in ['A', 'B', 'C']
        self.inst.write(u'ACTV%s' % str(trace_dict.keys()[trace_dict.values().index(trace.upper())]))

    def write_trace(self, trace):
        """Sets the trace A, B or C to write mode."""
        assert trace.upper() in ['A', 'B', 'C']
        self.inst.write(u'WRT%s' % trace.upper())

    def fix_trace(self, trace):
        assert trace.upper() in ['A', 'B', 'C']
        self.inst.write(u'FIX%s' % trace.upper())

    # DISPLAY #
    def set_noise_mask(self, val):
        """
        Displays the waveforms below the set value in masked form. (Unit: dBm)

        Parameters:
            val: int
                from 0 to -100 (1 step), OFF: -999
        """
        assert isinstance(val, int)
        assert ((-val > 0) and (-val < 100)) or val == -999
        self.inst.write(u'NMSK%i' % val)

    def set_3d_display(self, angle, z_scale, **kwargs):
        """
        Sets the screen to the 3-dimensional display mode.

        Parameters:
            angle: int
                Sets the angle of 3-dimensional display. (Unit: deg)
                from -50 to 50 (10 steps)
            z_scale: int
                 Sets the number of waveforms in the 3-dimensional display mode.
                 from 3 to 16 (1 step)

        Kwargs:
            memory: int
                Sets the memory for 3-dimensional display. 1st: 0; 2nd: 1.
        """
        assert angle in np.arange(-50, 51, 10)
        assert z_scale in np.arange(3, 17, 1)
        self.inst.write(u'3D')
        self.inst.write(u'ANGL%i' % angle)
        self.inst.write(u'ZSCL%i' % z_scale)
        if 'memory' in kwargs.keys():
            assert kwargs['memory'] in [0, 1]
            self.inst.write(u'MEM%i' % kwargs['memory'])

    def set_normal_display(self):
        """Sets the scren to the ordinary display mode."""
        self.inst.write(u'NORMD')

    def clear_graph(self):
        self.inst.write(u'CLR')

    # SYSTEM #
    def get_warnings(self):
        """Get a warning error number."""
        wmsgs = self.inst.query(u'WARN?')
        # Strip off other unicode part
        wmsgs = wmsgs.split(' ')
        wmsgs.pop(-1)    # the last one is u'\t\n'
        wmsgs = [str(msg) for msg in wmsgs]
        return [msg for msg in wmsgs[1:] if msg != '']

    def set_current_date_time(self):
        now = datetime.now()
        date = str(now.year % 100) + '.' + now.strftime("%m.%d")
        self.inst.write(u'DATE%s ' % date)
        self.inst.write(u'TIME%s ' % now.strftime("%H%M"))

    def set_theme(self, index):
        """Set the display color.

        Parameters:
            index: int
                1: bg: black, colorful grid
                2: bg: black, white grid
                3: bg: white, black grid
                4: bg: black, green grid
                5: bg: black, red grid
        """
        assert index in range(1, 6)
        self.inst.write(u'DEFCL%i' % index)

    def calibrate_with_internal_source(self):
        """Calibrates a wavelength by the internal light source."""
        self.inst.write(u'WCALS')

    def calibrate_with_external_source(self, a_wl):
        """Waveform absolute value calibration by the external light source.

        Parameters:
            a_wl: float
                from 600.000 to 1750.000 (0.001 step). (Unit: nm)
        """
        assert a_wl in np.arange(600, 1750.001, 0.001)
        self.inst.write(u'WCAL%.3f' % a_wl)

    def auto_offset(self, enable):
        assert isinstance(enable, bool)
        self.inst.write(u'ATOFS%i' % int(enable))

    def wl_shift(self, val):
        """Sets the amount of wavelength shift in nm.
        Parameters:
            val: float
                from -5.000 to 5.000 (0.001 step)
        """
        assert ((val >= -5.0) and (val <= 5.0))
        self.inst.write(u'WLSFT%.3f' % val)

    # OTHERS
    def set_service_request_permission(self, allow=True):
        self.inst.write(u'SRQ%i' % int(allow))

    def is_service_request_on(self):
        is_on = self.inst.query(u'SRQ?')
        return is_on

    def wait_until_task_is_done(self, timeout=25, checking_interval=0.05):
        """time unit: second"""
        if not self.is_service_request_on():
            print("Error: OSA service request is not on.")
            raise
        start_t = time.time()
        while True:
            status = self.inst.read_stb()
            if status == 65:
                return True
            if (time.time() - start_t) >= timeout:
                print("Error: OSA sweeping timeout.")
                raise TimeoutError
            time.sleep(checking_interval)

    # POWER METER MODE#
    def power_meas_repeat(self, wl_range=None, unit='dbm', show_relative_val=True):
        """
        Sets repeat measurement.

        Parameters:
            wl_range: list
                the wavelength range. min: 600; max: 1750.
            unit: str
                display power unit in 'dBm' or 'W'.
            show_relative_val: bool
                when true, display the relative power value, otherwise absolute value.
        """
        self.set_power_unit(unit)
        if wl_range:
            self._set_power_meas_area(wl_range)
        self.inst.write(u'PMPRT')
        self.display_relative_value(show_relative_val)

    def power_meas_single(self, wl_range=None, unit='dbm', show_relative_val=True):
        """
        Sets single measurement.

        Parameters:
            wl_range: list
                the wavelength range. min: 600; max: 1750.
            unit: str
                display power unit in 'dBm' or 'W'.
            show_relative_val: bool
                when true, display the relative power value, otherwise absolute value.
        """
        if wl_range:
            self._set_power_meas_area(wl_range)
        self.set_power_unit(unit)
        self.inst.write(u'PMSGL')
        self.display_relative_value(show_relative_val)

    def power_meas_stop(self):
        """Stops the power meter function."""
        self.inst.write(u'PMSTP')

    def _set_power_meas_area(self, wl_range):
        """
        Sets the measuring range.

        Parameters:
            wl_range: list, np.array
                the measuring range.
        """
        assert isinstance(wl_range, (list, np.ndarray))
        wl_start, wl_end = wl_range
        assert (wl_end > wl_start) & (wl_start >= 600) & (wl_end <= 1750)
        if wl_start >= 1000:
            indx = 3
        elif wl_end <= 1000:
            indx = 2
        else:
            indx = 0    # full range
        self.inst.write(u'AREA%i' % indx)

    def set_power_unit(self, unit):
        """
        Sets the display unit.

        Parameters:
            unit: str
                dBm (LOG value)
                W (linear value)
        """
        units = {'dbm': 0, 'w': 1}
        assert isinstance(unit, str)
        assert unit.lower() in units.keys(), "ValueError: invalid unit"
        self.inst.write(u'PMUNT%i' % units[unit.lower()])

    def display_relative_value(self, enable):
        """
        Selects absolute value or relative value for display value.

        Parameters:
            enable: bool
                when enabled (True) the relative value is 0 dB. When False, display absolute value.
        """
        assert isinstance(enable, bool)
        self.inst.write(u'REL%i' % int(enable))

    # OTHERS #
    def set_timeout(self, milliseconds):
        """
        Sets the timeout of the instrument in milliseconds.

        Args:
            milliseconds(float): The timeout in milliseconds
        """
        self.inst.timeout = milliseconds

    def header_on(self, enable):
        assert (enable in [0, 1, True, False])
        self.inst.write(u'HD%i' % int(enable))
        self.meas_with_header = enable

    def is_header_on(self):
        """Returns 1 if header is on, 0 if header is off."""
        return self.inst.query('HD?')

    def _get_spectrum(self, channel=None, r_start=1, r_end=None):
        """
        Returns the measured spectrum from a single reading of the instrument.
        Aliases to acquire. Does not work in 3D mode.
        ! Please do not directly call this method as it gets the spectrum directly without checking if sweeping is
        finished. Call get_spectrum instead.

        Parameters:
            channel: str
                Trace 'A', 'B' or 'C'
            r_start: str
                from 1 to 20001
            r_end: str
                from 1 to 20001
        Returns:
            tuple of arrays:
                The first array contains the wavelengths in nanometers.
                The second array contains the optical power in dBm.
        """
        if channel:
            channel = channel.upper()
            assert channel in ['A', 'B', 'C']
        else:
            channel = self.get_active_trace()
            # print("Active channel: {}".format(channel))
        assert isinstance(r_start, int) & (r_start >= 1) & (r_start <= 20001)

        if r_end is None:
            r_end = self.get_sampling_point()
        else:
            assert isinstance(r_end, int)
            assert (r_end >= r_start) & (r_end <= 20001)
        wl_string = self.inst.query(u'WDAT%sR%i-R%i' % (channel, r_start, r_end))
        pw_string = self.inst.query(u'LDAT%sR%i-R%i' % (channel, r_start, r_end))
        # if self.meas_with_header:
        #     wl_string = wl_string.split(' ')
        #     pw_string = pw_string.split(' ')
        #     wl_unit = str(wl_string.pop(0)).lower()
        #     pw_unit = str(pw_string.pop(0)).lower()
        #     wl_string = ''.join(wl_string)
        #     pw_string = ''.join(pw_string)
        # else:
        #     wl_unit = 'nm'
        #     pw_unit = 'dBm'

        # The last two elements of power_string are '\r' and '\n'
        # The first number in power list is the value of r_end;
        wl_list = wl_string.split(',')[1:]
        power_list = pw_string.split(',')[1:]

        # wl_list = np.array(wl_string[:-2].split(','))[1:].astype(np.float)
        # pw_list = np.array(pw_string[:-2].split(','))[1:].astype(np.float)
        wl_array = np.array(wl_list).astype(np.float64)
        pw_array = np.array(power_list).astype(np.float64)
        return wl_array, pw_array

    def get_spectrum(self, channel='A', r_start=1, sampling_points=None):
        """Add sweeping status checking."""
        self.set_service_request_permission(allow=True)
        osa_wl, osa_pw = None, None
        try:
            self.single_sweep()
            # if self.wait_until_task_is_done(timeout=200):
            osa_wl, osa_pw = \
                self._get_spectrum(channel=channel, r_start=r_start, r_end=r_start + sampling_points - 1)
        except:
            print("[{}]".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
            print(traceback.format_exc())
            self.clear_graph()
        finally:
            self.set_service_request_permission(allow=False)
        return osa_wl, osa_pw

    def save_spectrum_data(self, abs_filepath, digit=False, show_plt=False):
        active_trace = self.get_active_trace()
        wl, pw = self._get_spectrum(active_trace)

        info_str = self._create_info_str(len(wl))
        if not file_accessible(abs_filepath):
            raise IOError("File not accessible.")
        with open(abs_filepath, "w") as f:
            f.write(info_str)
            column_str = "Index\tWavelength [{}]\tPower (trace {}) [{}]\n".format(wl_unit, active_trace, pw_unit)
            f.write(column_str)
            for i in range(len(wl)):
                pw_val = pw[i].round(decimals=digit)
                f.write("{}\t{}\t{}\n".format(i + 1, wl[i], pw_val))

        if show_plt:
            plt.plot(wl, pw)
            plt.ylabel('Power ({})'.format(pw_unit))
            plt.xlabel('Wavelength ({})'.format(wl_unit))
            plt.show()

    def plot_active_spectrum(self):
        wl, pw = self._get_spectrum()

        assert wl is not None and pw is not None
        plt.plot(wl, pw)
        plt.ylabel('Power (dBm})')
        plt.xlabel('Wavelength (nm)')
        plt.show()

    def _create_info_str(self, nb_entries):
        now = datetime.now()
        date_time_info = "Measuring time: " + now.strftime("%d.%m.%Y at %H:%M:%S") + "; "
        wl_info = "Wavelength range: {}, resolution: {}; ".format(self.get_wl_range(), self.get_resolution())
        nb_entries_info = "Number of entries: {}".format(nb_entries)
        info_str = date_time_info + wl_info + nb_entries_info
        return info_str + '\n'

    def initialize_hardware(self):
        """Initializes the hardware (Resets instrument to factory default state)."""
        self.inst.write(u'*RST')
        self.meas_with_header = self.is_header_on()

    def initialize_nonvolatile_data(self):
        """Initializes data except program/memory."""
        self.inst.write(u'INIT')

    def set_output_digit(self, digit):
        """
        Sets the number of decimal digits during level data (log) output via GPIB port.

        Parameters:
            digit: int
                 "2" for two decimal digits immediately after power-on
                 "3" for three decimal digits"""
        assert digit in [2, 3]
        self.inst.write(u'LDTDIG%i' % digit)

    def clean_up(self):
        self.inst.close()
        self.osa_rm.close()
        print("[INFO] Ando AQ6317B OSA connection closed.")


if __name__ == "__main__":
    osa = ANDO_AQ6317B(u'GPIB0::16::INSTR')
    # print(osa.identify())
    osa.header_on(False)
    osa.single_sweep()
    time.sleep(3)
    osa.set_sampling_point(nb_points=1001)
    wl_array, pw_array = osa._get_spectrum(channel='A', r_start=1, r_end=1001)
    plt.plot(wl_array, pw_array)
    plt.xlabel('Wavelength (nm)')
    plt.ylabel('Power (dBm)')   
    plt.show()
    # osa.clean_up()
