import numpy as np
import os
import matplotlib.pyplot as plt

from ripple.field import Transverse<PERSON>ield
from ripple.field_distribution import GaussianDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_file
from ripple.field_distribution import ImportedCSTFieldDistribution


c_wl = 1e3     # um
lens_len = 5e3
souce_w0 = 2.8e3 / 2

target_r = 86.5e3/2
source_z_pos = 40e3

source_field = ImportedCSTFieldDistribution(cst_export_file_path=r'C:\Users\<USER>\Documents\WORK\02_Research\12_THz lens\E-Field.txt',
                                              cut_axis='z', rough_cut_pos=40, skiprows=0, Nix=512, Niy=1024)
source_field = TransverseField(distribution=source_field, z0=0, refractive_index=1., wavelength=c_wl)

target_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=target_r, w0y=target_r),
                               z0=223e3, refractive_index=1, wavelength=source_field.wavelength)
print(f"Source field resolution: {source_field.res_x, source_field.res_y}")
# source_field.view(cmap='viridis')
# plt.show()

# Test interpolation
# xi = np.linspace(source_field.x_range[0], source_field.x_range[1], num=1024)
# yi = np.linspace(source_field.y_range[0], source_field.y_range[1], num=1024)
# xxi, yyi = np.meshgrid(xi, yi)
# source_field.interpolate_field(xxi.T, yyi.T)
# source_field.view(cmap='viridis')
# plt.show()

# monitor_yz = FieldMonitor(monitor_type='yz', position=0, saving_path=None)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_power_per_z=True)

concave_lens = Lens(first_surface_type='ConoidalSurface',
                    first_surface_parameters={'x0': 0, 'y0': 0, 'z0': 35e3 - (source_z_pos - 20.79e3),
                                              'rho': -1/35e3, 'kappa': 0},
                    second_surface_type='ConoidalSurface',
                    second_surface_parameters={'x0': 0, 'y0': 0, 'z0': 40e3 - (source_z_pos - 20.79e3),
                                               'rho': 1/35e3, 'kappa': 0},
                    max_radius=25e3,
                    refractive_index=np.sqrt(2.1),
                    priority=1)

factor = source_field.wavelength / source_field.n
sampling_x = int(factor / source_field.res_x)
sampling_y = int(factor / source_field.res_y)

scene = CouplingScene(input_field=source_field, target_field=None,
                      background_material_index=1.,
                      optical_structures=[concave_lens],
                      sim_size_xy=[100e3, 100e3],
                      sim_z_end=None,
                      refine_material_res=True,
                      wavelength_sampling_xy=[6, 6],
                      # wavelength_sampling_xy=[sampling_x, sampling_y],
                      # wavelength_sampling_xy=1,
                      material_wavelength_sampling_z=4,
                      background_wavelength_sampling_z=4,
                      monitors=[monitor_xz],
                      boundary_condition='PML', boundary_parameters=None)

print(f"{scene.target_material_res_xy = }")
print(f"{scene.target_material_res_z = }")
print(f"{scene.target_background_res_z = }")

# save_to_mat(outpath=r'C:\Users\<USER>\Documents\PycharmProjects\bvwpm\scripts\THz_lens\data\only_concave\input_field.mat',
#             data_dict={'E_field': scene.input_field.field,
#                        'x_grid': scene.input_field.x_grid, 'y_grid': scene.input_field.y_grid})


if __name__ == "__main__":
    scene.preview(mode='xz', position=0)
    scene.preview(mode='xy', position=18e3)
    # scene.preview(mode='yz', position=0)
    plt.show()

    # scene.view_3D_mesh()  # FIXME

    run_sim(scene, optimize_structure=False)
    # plot_monitor_from_file(path=r'C:\Users\<USER>\Documents\PycharmProjects\bvwpm\scripts\THz_lens\data\only_concave\Monitor_xy_concave_original_884.pkl')
    # plot_monitor_from_file(path=r'C:\Users\<USER>\Documents\PycharmProjects\bvwpm\scripts\THz_lens\data\only_concave\Monitor_xz_concave_original_884.pkl')
    # plt.show()

