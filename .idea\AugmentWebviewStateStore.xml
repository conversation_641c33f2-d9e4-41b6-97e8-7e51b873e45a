<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;9cdc59f5-9f7c-4fe6-8e6d-837645fca8c1&quot;,&quot;conversations&quot;:{&quot;9cdc59f5-9f7c-4fe6-8e6d-837645fca8c1&quot;:{&quot;id&quot;:&quot;9cdc59f5-9f7c-4fe6-8e6d-837645fca8c1&quot;,&quot;name&quot;:&quot;Welcome to the Augment Agent&quot;,&quot;createdAtIso&quot;:&quot;2025-07-01T08:02:06.296Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-21T14:46:33.287Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;ee4f9ce7-d4bf-4f52-9cc6-1991b2eeebb8&quot;,&quot;request_message&quot;:&quot;The y range in the plot should be from 30 to 140 and the label should also show where is 30 and 140&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;The y range in the plot should be from 30 to 140 and the label should also show where is 30 and 140&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01F8AYbUb9cU6pTDuEKCC5rf&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;Find the plotting code that contains ax.set_ylim([30, 140]) and show the complete context including how the y-axis labels are currently configured, what type of plot this is, and any existing tick or label settings\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;I'll help you adjust the y-axis range and labels to show values from 30 to 140. Let me first examine the current code to understand the plotting context.\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;The y range in the plot should be from 30 to 140 and the label should also show where is 30 and 140&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-21T14:45:47.855Z&quot;,&quot;response_text&quot;:&quot;I'll help you adjust the y-axis range and labels to show values from 30 to 140. Let me first examine the current code to understand the plotting context.\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;e5cdc539-6827-468d-a414-193ab8782021&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01RMGdupCPeXgdStxSCfQQCG&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;Find all plotting code that uses matplotlib, pyplot, or any plotting libraries, including any code that sets y-axis limits, labels, or ticks\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me search more broadly for plotting code in the codebase:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01F8AYbUb9cU6pTDuEKCC5rf&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-21T14:45:56.505Z&quot;,&quot;response_text&quot;:&quot;Let me search more broadly for plotting code in the codebase:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;bf73faf0-9397-4e26-9257-09a20f5cc3b7&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01QjpYdidUYe7y8CGWDCjsXL&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;.\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me check the directory structure to understand the codebase better:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01RMGdupCPeXgdStxSCfQQCG&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-21T14:46:05.030Z&quot;,&quot;response_text&quot;:&quot;Let me check the directory structure to understand the codebase better:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;396d0f44-2c70-47cb-8ded-29c515502bb5&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01SpYmfRjPZfCwzaoGbnPYLA&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;Find Python files that contain matplotlib plotting code, specifically looking for any code that sets y-axis limits using ylim, set_ylim, or similar functions, and any y-axis labeling or tick configuration\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me search for Python files that might contain plotting code with y-axis settings:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./EDWA\n./EDWA\\noise_figure.py\n./MFD_setup\n./MFD_setup\\mfd_fit\n./MFD_setup\\polarization_measurement.py\n./OBR_processing\n./OBR_processing\\OBR_rename.py\n./OBR_processing\\cal_delayline_mismatch.py\n./OBR_processing\\obr_data_processing.py\n./OBR_processing\\obr_data_processing_CLEO_plot.py\n./OSA\n./OSA\\OSA_spectrum\n./OSA\\__pycache__\n./OSA\\plot_EDWA_spectrum.py\n./OSA\\plot_QDMLLD_spectrum.py\n./PNA\n./PNA\\__pycache__\n./PNA\\frequency_noise.npy\n./PNA\\offset_frequency.npy\n./PNA\\plot_PN.py\n./PNA\\plot_PN_from_IQ_vectors.py\n./PNA\\plot_RF_phase_noise.py\n./PNA\\plot_RF_spectrum.py\n./PNA\\utils.py\n./utils\n./utils\\Lorentzian_linewidth.py\n./utils\\__init__.py\n./utils\\__pycache__\n./utils\\formatting.py\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01QjpYdidUYe7y8CGWDCjsXL&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-21T14:46:09.098Z&quot;,&quot;response_text&quot;:&quot;Let me search for Python files that might contain plotting code with y-axis settings:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;9b57ec47-d3b8-45b0-8122-ce6f3aab1518&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01SUkeFKj95MeWCrnWJhSdcg&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;PNA/plot_PN.py\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me check some of the plotting files directly:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01SpYmfRjPZfCwzaoGbnPYLA&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-21T14:46:16.245Z&quot;,&quot;response_text&quot;:&quot;Let me check some of the plotting files directly:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;953ed464-e44f-4049-b16b-6d4a156104bb&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01PihfNFgAL9ap6TkWyvoJiS&quot;,&quot;tool_name&quot;:&quot;str-replace-editor&quot;,&quot;input_json&quot;:&quot;{\&quot;command\&quot;: \&quot;str_replace\&quot;, \&quot;path\&quot;: \&quot;PNA/plot_PN.py\&quot;, \&quot;instruction_reminder\&quot;: \&quot;ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.\&quot;, \&quot;old_str\&quot;: \&quot;    ax.set_ylabel('Intrinsic linewidth [kHz]')\\n    ax.set_ylim([30, 140])\\n    ax.grid(True, which='major')\&quot;, \&quot;new_str\&quot;: \&quot;    ax.set_ylabel('Intrinsic linewidth [kHz]')\\n    ax.set_ylim([30, 140])\\n    # Set y-axis ticks to explicitly show the range boundaries\\n    ax.set_yticks([30, 50, 70, 90, 110, 130, 140])\\n    ax.grid(True, which='major')\&quot;, \&quot;old_str_start_line_number\&quot;: 210, \&quot;old_str_end_line_number\&quot;: 212}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Perfect! I found the plotting code. I can see that in the `plot_intrinsic_linewidth_over_channel` function on line 211, there's already a y-axis limit set to `[30, 140]`, but the y-axis labels (ticks) are not explicitly configured to show the 30 and 140 values. Let me examine this function more closely and then make the necessary changes.\n\n&lt;augment_code_snippet path=\&quot;PNA/plot_PN.py\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```python\ndef plot_intrinsic_linewidth_over_channel(data_folder, start_line=174, nr_points=1201, save_fig=False):\n    # ... code above ...\n    ax.set_ylabel('Intrinsic linewidth [kHz]')\n    ax.set_ylim([30, 140])\n    ax.grid(True, which='major')\n```\n&lt;/augment_code_snippet&gt;\n\nNow I'll modify the code to ensure that the y-axis labels explicitly show the 30 and 140 values. I need to add y-axis tick configuration to make these boundary values visible.&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;Perfect! I found the plotting code. I can see that in the `plot_intrinsic_linewidth_over_channel` function on line 211, there's already a y-axis limit set to `[30, 140]`, but the y-axis labels (ticks) are not explicitly configured to show the 30 and 140 values. Let me examine this function more closely and then make the necessary changes.\n\n````python path=PNA/plot_PN.py mode=EXCERPT\ndef plot_intrinsic_linewidth_over_channel(data_folder, start_line=174, nr_points=1201, save_fig=False):\n    # ... code above ...\n    ax.set_ylabel('Intrinsic linewidth [kHz]')\n    ax.set_ylim([30, 140])\n    ax.grid(True, which='major')\n````\n\nNow I'll modify the code to ensure that the y-axis labels explicitly show the 30 and 140 values. I need to add y-axis tick configuration to make these boundary values visible.\n&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Note: Path was automatically corrected from 'PNA/plot_PN.py' to 'C:\\Users\\<USER>\\Documents\\PycharmProject\\process_measurement\\PNA\\plot_PN.py'.\n\nHere's the result of running `cat -n` on C:\\Users\\<USER>\\Documents\\PycharmProject\\process_measurement\\PNA\\plot_PN.py:\n     1\timport os.path\n     2\timport numpy as np\n     3\timport matplotlib\n     4\timport matplotlib.pyplot as plt\n     5\timport glob\n     6\tfrom scipy.constants import c\n     7\t\n     8\tfrom utils import read_csv, get_average_values_from_dir\n     9\tfrom process_measurement.utils.formatting import define_scale, format_large_number\n    10\tplt.rcParams['font.family'] = 'arial'\n    11\tplt.rcParams['font.size'] = 7\n    12\tplt.rcParams['savefig.dpi'] = 300\n    13\t\n    14\t\n    15\tdef plot_freq_noise(x_list, y_list, labels, colors, title='', font_size=14, saving_dir=None):\n    16\t    color_new = matplotlib.colormaps['Paired'].colors\n    17\t\n    18\t    fig, ax = plt.subplots(figsize=(2.3, 1.65))\n    19\t\n    20\t    # Free running from IQ vector\n    21\t    freq = np.load('offset_frequency.npy')\n    22\t    noise = np.load('frequency_noise.npy')\n    23\t    ax.plot(freq, noise, '-', alpha=1, color='tab:pink', linewidth=0.7, label='w/o feedback, 1533 nm')\n    24\t\n    25\t    for i, (x_values, y_values, label, color) in enumerate(zip(x_list, y_list, labels, colors)):\n    26\t        y_values **= 2\n    27\t        ax.plot(x_values, y_values, '-', alpha=1, color=color, label=label, linewidth=0.7)\n    28\t\n    29\t    ax.set_xscale('log')\n    30\t    ax.set_yscale('log')\n    31\t\n    32\t    title = title if title else 'Optical frequency noise'\n    33\t    # ax.set_title(title, fontsize=font_size+2)\n    34\t    ax.set_xlabel(f'Frequency offset [Hz]')\n    35\t    # ax.set_ylabel(r'Frequency noise [Hz$^2$/Hz]')\n    36\t    ax.set_ylabel(r'$S_F$($f$) [Hz$^2$/Hz]')\n    37\t    ax.tick_params(axis='both', direction='in', which='both')\n    38\t\n    39\t    ax.set_xlim([10**2, 10**7])\n    40\t    ax.set_ylim([10**3, 10**18])\n    41\t    ax.set_xticks([10**2, 10**3, 10**4, 10**5, 10**6, 10**7])\n    42\t    ax.set_yticks([10**16, 10**13, 10**10, 10**7, 10**4])\n    43\t    ax.grid(True, which=\&quot;both\&quot;, ls=\&quot;--\&quot;, alpha=0.3)\n    44\t    # ax.axhline(y=10**7, color='r', linewidth=0.5, linestyle='--')\n    45\t    # plt.legend(frameon=False, loc='upper right', handlelength=1, fontsize=5.2, bbox_to_anchor=(1.02, 1.02))\n    46\t    plt.tight_layout()\n    47\t    ax.xaxis.set_label_coords(.5, -0.16)\n    48\t    ax.yaxis.set_label_coords(-0.15, 0.5)\n    49\t    if saving_dir:\n    50\t        print(f'Saved to {os.path.join(saving_dir, title + \&quot;.png\&quot;)}')\n    51\t        fig.savefig(fname=os.path.join(saving_dir, title + '.png'), dpi=1200, transparent=True)\n    52\t    plt.show()\n    53\t\n    54\t\n    55\tdef plot_single_freq_noise(x_values, y_values, title='', save_filename=None, font_size=14, saving_dir=None, show_plot=True):\n    56\t    fig, ax = plt.subplots(figsize=(8, 5))\n    57\t    y_values **= 2\n    58\t    ax.plot(x_values, y_values, '-', alpha=0.8, linewidth=2)\n    59\t\n    60\t    ax.set_xscale('log')\n    61\t    ax.set_yscale('log')\n    62\t\n    63\t    title = title if title else 'Optical frequency noise'\n    64\t    ax.set_title(title, fontsize=font_size+2)\n    65\t    ax.set_xlabel(f'Frequency offset (Hz)', fontsize=font_size)\n    66\t    ax.set_ylabel(r'Frequency noise (Hz/$\\sqrt{Hz}$)', fontsize=font_size)\n    67\t    ax.tick_params(axis='x', labelsize=font_size)\n    68\t    ax.tick_params(axis='y', labelsize=font_size)\n    69\t\n    70\t    ax.set_xlim([min(x_values), max(x_values)])\n    71\t    ax.set_ylim([10e2, 10e10])\n    72\t    ax.grid(True, which=\&quot;both\&quot;, ls=\&quot;--\&quot;, alpha=0.3)\n    73\t    plt.tight_layout()\n    74\t    if saving_dir:\n    75\t        fig.savefig(fname=os.path.join(saving_dir, save_filename if save_filename else title + '.png'), dpi=1200,\n    76\t                    transparent=False)\n    77\t    if show_plot:\n    78\t        plt.show()\n    79\t    plt.clf()\n    80\t\n    81\tdef main():\n    82\t    files = [\n    83\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\locked_spectrum_1527\\225mA 13.5mA_heat_1526.99nm_newport_valid1p93.CSV',\n    84\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\locked_spectrum_1533\\225mA 13.5mA_heat_1532.99nm_newport_valid1p16.CSV',\n    85\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\locked_spectrum_1539\\225mA 13.5mA_heat_1539.01nm_newport_valid1p76.CSV'\n    86\t        ]\n    87\t    labels = ['1527 nm', '1533 nm', '1539 nm']\n    88\t    start_lines = [174, 174, 174]\n    89\t    colors = ['tab:orange', 'red', 'tab:blue', 'tab:purple']\n    90\t\n    91\t    x_list, y_list = [], []\n    92\t    for file, label, start_line in zip(files, labels, start_lines):\n    93\t        x_values, y_values = read_csv(file, start_line=start_line)\n    94\t        x_list.append(x_values)\n    95\t        y_list.append(y_values)\n    96\t\n    97\t    x_list = np.array(x_list)\n    98\t    y_list = np.array(y_list)\n    99\t    plot_freq_noise(x_list, y_list, labels, colors,\n   100\t                    title='Phase noise',\n   101\t                    saving_dir=r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025')\n   102\t\n   103\tdef freq_noise_CLEO():\n   104\t    files = [\n   105\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\07_Optical_linewidth\\Locked\\1527p14\\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p7mA_newfocus1527p14nm_rec4.csv',\n   106\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\07_Optical_linewidth\\Locked\\1533\\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p9mA_rec5.csv',\n   107\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\07_Optical_linewidth\\Locked\\1539p96\\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p9mA_newfocus1539p96nm_rec1.csv'\n   108\t    ]\n   109\t    labels = ['w/ feedback, 1527 nm', 'w/ feedback, 1533 nm', 'w/ feedback, 1539 nm']\n   110\t    start_lines = [184, 184, 184]\n   111\t    colors = ['red', 'green', 'blue']\n   112\t\n   113\t    x_list, y_list = [], []\n   114\t    for file, label, start_line in zip(files, labels, start_lines):\n   115\t        x_values, y_values = read_csv(file, start_line=start_line)\n   116\t        x_list.append(x_values)\n   117\t        y_list.append(y_values)\n   118\t\n   119\t    x_list = np.asarray(x_list)\n   120\t    y_list = np.asarray(y_list)\n   121\t    plot_freq_noise(x_list, y_list, labels, colors,\n   122\t                    title='Phase noise',\n   123\t                    font_size=22, saving_dir=r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\07_Optical_linewidth')\n   124\t\n   125\tdef plot_single_csv_file(file_path, start_line, nr_points, title=None, save_to_folder=None, save_filename=None, show_plot=True):\n   126\t    x_values, y_values = read_csv(file_path, start_line=start_line, nr_points=nr_points)\n   127\t    # x_values, y_values = get_average_values_from_dir(folder, start_line=start_line, nr_points=nr_points, delimiter='\\t')\n   128\t    plot_single_freq_noise(x_values, y_values, title='Phase noise' if title is None else title, save_filename=save_filename,\n   129\t                           font_size=22, saving_dir=save_to_folder, show_plot=show_plot)\n   130\t\n   131\tdef estimate_intrinsic_linewidth(freq_Hz, PSD_Hz_per_sqrt_Hz, f_min=1e6, f_max=10e6, show_plot=False, plot_title=None):\n   132\t    S_nu = PSD_Hz_per_sqrt_Hz ** 2  # in Hz^2/Hz\n   133\t\n   134\t    # Mask the region for white noise floor\n   135\t    mask = (freq_Hz &gt;= f_min) &amp; (freq_Hz &lt;= f_max)\n   136\t    S_white = S_nu[mask]\n   137\t\n   138\t    # Step 3: Estimate white noise level (average)\n   139\t    # f_white = freq_Hz[mask]\n   140\t    S_white_avg = np.median(S_white)\n   141\t\n   142\t    # Step 4: Calculate intrinsic linewidth\n   143\t    delta_nu_intrinsic = np.pi * S_white_avg  # Hz\n   144\t\n   145\t    # Step 5: Print result\n   146\t    print(f\&quot;Estimated intrinsic linewidth: {delta_nu_intrinsic/1e3:.2f} kHz\&quot;)\n   147\t    #\n   148\t    # from sklearn.linear_model import HuberRegressor\n   149\t    # # Fit flat line (slope ≈ 0)\n   150\t    # X = np.log10(f_white).reshape(-1, 1)\n   151\t    # y = np.log10(S_white)\n   152\t    # model = HuberRegressor().fit(X, y)\n   153\t    # log_S_fit = model.predict(X)\n   154\t    # S_white_fit = 10 ** np.mean(log_S_fit)\n   155\t    # delta_nu_fit = np.pi * S_white_fit\n   156\t    # print(f\&quot;Estimated intrinsic linewidth by Huberregressor: {delta_nu_fit}\&quot;)\n   157\t\n   158\t    if show_plot:\n   159\t        # Optional: Plot for verification\n   160\t        plt.loglog(freq_Hz, S_nu, label=\&quot;Sν(f) [Hz²/Hz]\&quot;)\n   161\t        plt.axhline(S_white_avg, color='r', linestyle='--', label='White noise level')\n   162\t        # plt.axhline(S_white_fit, color='b', linestyle='--', label='White noise level - Huber regressor')\n   163\t        plt.axvspan(f_min, f_max, color='gray', alpha=0.2, label='White noise region')\n   164\t        plt.xlabel(\&quot;Frequency offset (Hz)\&quot;)\n   165\t        plt.ylabel(\&quot;Frequency noise PSD (Hz²/Hz)\&quot;)\n   166\t        plt.legend()\n   167\t        plt.title(\&quot;Frequency Noise PSD and White Noise Estimate\&quot; if plot_title is None else plot_title)\n   168\t        plt.grid(True, which='both')\n   169\t        plt.show()\n   170\t    return delta_nu_intrinsic\n   171\t\n   172\tdef plot_folder(folder_path, start_line=174, nr_points=1201, save_to_folder=None):\n   173\t    csv_files = glob.glob(os.path.join(folder_path, '*.csv'), recursive=False)\n   174\t    if not os.path.isdir(save_to_folder):\n   175\t        os.mkdir(save_to_folder)\n   176\t    for csv_file in csv_files:\n   177\t        basename = os.path.basename(csv_file)\n   178\t        file_name_list = basename.split('_')\n   179\t        plot_title = f\&quot;Phase noise of {file_name_list[1]}-{file_name_list[-1][:-len('.csv')]}\&quot;\n   180\t        plot_single_csv_file(file_path=csv_file, start_line=start_line, nr_points=nr_points, title=plot_title,\n   181\t                             save_filename=basename[:-len('.csv')], save_to_folder=save_to_folder, show_plot=False)\n   182\t\n   183\tdef extract_channel_nr_from_filename(filename):\n   184\t    basename = os.path.basename(filename)\n   185\t    channel_nr = basename.split('_')[1][2:]\n   186\t    return int(channel_nr)\n   187\t\n   188\tdef plot_intrinsic_linewidth_over_channel(data_folder, start_line=174, nr_points=1201, save_fig=False):\n   189\t    channel_freq_mapping = {1: 194.4660, 2: 194.5650, 3: 194.6652, 4: 194.7640, 5: 194.8640, 6: 194.9630, 7: 195.0630,\n   190\t                            8: 195.1620, 9: 195.2610, 10: 195.3610, 11: 195.4600, 12: 195.5600, 13: 195.6590, 14: 195.7590}\n   191\t    channels = []\n   192\t    channel_linewidth = []\n   193\t    file_list = glob.glob(os.path.join(data_folder, '*.csv'), recursive=False)\n   194\t    for csv_file in file_list:\n   195\t        x_values, y_values = read_csv(csv_file, start_line=start_line, nr_points=nr_points)\n   196\t        ch_id = extract_channel_nr_from_filename(csv_file)\n   197\t        print(f\&quot;Channel: {ch_id}\&quot;)\n   198\t        channels.append(ch_id)\n   199\t        channel_linewidth.append(estimate_intrinsic_linewidth(freq_Hz=x_values, PSD_Hz_per_sqrt_Hz=y_values,\n   200\t                                                              f_min=1e6, f_max=10e6, show_plot=False, plot_title=f'FN of channel {ch_id}'))\n   201\t    freqs = [channel_freq_mapping[ch] for ch in channels]\n   202\t    fig, ax = plt.subplots(figsize=(3, 1.5))\n   203\t\n   204\t    ax.plot(freqs, np.asarray(channel_linewidth)/1e3, 'o', markersize=3)\n   205\t    ax.set_xlabel('Frequency [THz]')\n   206\t\n   207\t    # ax.plot(c/(np.asarray(freqs)*1e12)*1e9, np.asarray(channel_linewidth)/1e3, 'o', makersize=5)\n   208\t    # ax.set_xlabel('Wavelength [nm]')\n   209\t\n   210\t    ax.set_ylabel('Intrinsic linewidth [kHz]')\n   211\t    ax.set_ylim([30, 140])\n   212\t    ax.grid(True, which='major')\n   213\t\n   214\t    plt.tight_layout()\n   215\t    if save_fig:\n   216\t        plt.savefig(os.path.join(data_folder, 'IntrLinwidth_vs_freq.png'), transparent=False, dpi=300)\n   217\t    plt.show()\n   218\t\n   219\t\n   220\tdef compare_method():\n   221\t    files = [\n   222\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\locked_spectrum_1527\\225mA 13.5mA_heat_1526.99nm_newport_valid1p93.CSV',\n   223\t        ]\n   224\t    labels = ['1527 nm PN', '1527 nm IQ']\n   225\t    start_lines = [174, 174, 174]\n   226\t    colors = ['tab:orange', 'red', 'tab:blue', 'tab:purple']\n   227\t\n   228\t    x_list, y_list = [], []\n   229\t\n   230\t    for file, label, start_line in zip(files, labels, start_lines):\n   231\t        x_values, y_values = read_csv(file, start_line=start_line)\n   232\t        x_list.append(x_values)\n   233\t        y_list.append(y_values)\n   234\t\n   235\t    x_list = np.array(x_list)\n   236\t    y_list = np.array(y_list)\n   237\t    plot_freq_noise(x_list, y_list, labels, colors,\n   238\t                    title='Phase noise',\n   239\t                    font_size=22, saving_dir=r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025')\n   240\t\n   241\t\n   242\tif __name__ == '__main__':\n   243\t    # compare_method()\n   244\t    # plot_single_csv_file()\n   245\t    # freq_noise_CLEO()\n   246\t    # plot_folder(folder_path=r'R:\\Bao\\Hybrid_comb\\20250630_locking_FN',\n   247\t    #             start_line=174, nr_points=1201,\n   248\t    #             save_to_folder=r'R:\\Bao\\Hybrid_comb\\20250630_locking_FN\\plots')\n   249\t    # file_path = r'R:\\Bao\\Hybrid_comb\\20250630_locking_FN\\FN_1525p0_QDMLLD_locked_LaserCurrent_250mA_HeaterCurrent_12mA_laserTemp30deg_chipTemp30deg_afterWS_2.csv'\n   250\t    # x_values, y_values = read_csv(file_path, start_line=174, nr_points=1201)\n   251\t    # estimate_intrinsic_linewidth(freq_Hz=x_values, PSD_Hz_per_sqrt_Hz=y_values, show_plot=True)\n   252\t\n   253\t    plot_intrinsic_linewidth_over_channel(data_folder = r'R:\\Bao\\Hybrid_comb\\20250630_locking_FN\\fit_linewidth',\n   254\t                                          save_fig=True)\nTotal lines in file: 254\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01SUkeFKj95MeWCrnWJhSdcg&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-21T14:46:19.971Z&quot;,&quot;response_text&quot;:&quot;Perfect! I found the plotting code. I can see that in the `plot_intrinsic_linewidth_over_channel` function on line 211, there's already a y-axis limit set to `[30, 140]`, but the y-axis labels (ticks) are not explicitly configured to show the 30 and 140 values. Let me examine this function more closely and then make the necessary changes.\n\n````python path=PNA/plot_PN.py mode=EXCERPT\ndef plot_intrinsic_linewidth_over_channel(data_folder, start_line=174, nr_points=1201, save_fig=False):\n    # ... code above ...\n    ax.set_ylabel('Intrinsic linewidth [kHz]')\n    ax.set_ylim([30, 140])\n    ax.grid(True, which='major')\n````\n\nNow I'll modify the code to ensure that the y-axis labels explicitly show the 30 and 140 values. I need to add y-axis tick configuration to make these boundary values visible.\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;0b34de1c-193a-4a62-ad90-9c72dafb08e5&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;Perfect! I've successfully modified the plotting code to:\n\n1. **Keep the y-axis range from 30 to 140** (this was already set with `ax.set_ylim([30, 140])`)\n2. **Add explicit y-axis tick labels** that show where 30 and 140 are located, along with intermediate values for better readability\n\nThe changes I made:\n- Added `ax.set_yticks([30, 50, 70, 90, 110, 130, 140])` to explicitly set the y-axis tick marks\n- This ensures that both the minimum (30) and maximum (140) values are clearly labeled on the y-axis\n- I included intermediate tick marks (50, 70, 90, 110, 130) to provide good granularity for reading values from the plot\n\nThe plot will now clearly show:\n- Y-axis range from 30 to 140 kHz\n- Tick marks at 30, 50, 70, 90, 110, 130, and 140 kHz\n- Grid lines aligned with these tick marks for easy reading\n\nThis modification is in the `plot_intrinsic_linewidth_over_channel` function which appears to be plotting intrinsic linewidth measurements across different channels. The y-axis now explicitly shows the boundary values you requested (30 and 140) along with intermediate reference points.\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Note: Path was automatically corrected from 'PNA/plot_PN.py' to 'C:\\Users\\<USER>\\Documents\\PycharmProject\\process_measurement\\PNA\\plot_PN.py'.\n\nSuccessfully edited the file C:\\Users\\<USER>\\Documents\\PycharmProject\\process_measurement\\PNA\\plot_PN.py.\nResult for str_replace for entry with index [0]:\nReplacement successful.\nnew_str starts at line 210 and ends at line 214.\n\nReview the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).\nEdit the file again if necessary.\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01PihfNFgAL9ap6TkWyvoJiS&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Documents/PycharmProject/process_measurement&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-21T14:46:33.287Z&quot;,&quot;response_text&quot;:&quot;Perfect! I've successfully modified the plotting code to:\n\n1. **Keep the y-axis range from 30 to 140** (this was already set with `ax.set_ylim([30, 140])`)\n2. **Add explicit y-axis tick labels** that show where 30 and 140 are located, along with intermediate values for better readability\n\nThe changes I made:\n- Added `ax.set_yticks([30, 50, 70, 90, 110, 130, 140])` to explicitly set the y-axis tick marks\n- This ensures that both the minimum (30) and maximum (140) values are clearly labeled on the y-axis\n- I included intermediate tick marks (50, 70, 90, 110, 130) to provide good granularity for reading values from the plot\n\nThe plot will now clearly show:\n- Y-axis range from 30 to 140 kHz\n- Tick marks at 30, 50, 70, 90, 110, 130, and 140 kHz\n- Grid lines aligned with these tick marks for easy reading\n\nThis modification is in the `plot_intrinsic_linewidth_over_channel` function which appears to be plotting intrinsic linewidth measurements across different channels. The y-axis now explicitly shows the boundary values you requested (30 and 140) along with intermediate reference points.\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;request_id&quot;:&quot;4dae4eda-ca54-4b89-a2dc-5fcba6da703f&quot;,&quot;uuid&quot;:&quot;f6baae9e-6286-43e8-af68-39ed7ee1d6a0&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1753109201722,&quot;seen_state&quot;:&quot;seen&quot;}],&quot;feedbackStates&quot;:{&quot;temp-fe-0ba9cdbb-1342-4c5c-9b8f-e443c4637698&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-030d8f83-bb8e-4814-927d-86567802b55f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-4f623497-ff91-4d32-8f59-565aea4bbd09&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-7981b958-fe54-4571-a69c-44df17131620&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-4a74aeae-f482-48ad-aaba-d5052db7915b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-e42edef0-55fc-4c93-a037-61363fd8e31a&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-30578d1a-333f-4c92-a07e-4211a982ff37&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-99a86363-0eff-42a7-bc7b-5b8d514265c3&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-7d64ece4-0f56-4178-be03-03e0e30922f2&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;ee4f9ce7-d4bf-4f52-9cc6-1991b2eeebb8;toolu_vrtx_01F8AYbUb9cU6pTDuEKCC5rf&quot;:{&quot;requestId&quot;:&quot;ee4f9ce7-d4bf-4f52-9cc6-1991b2eeebb8&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01F8AYbUb9cU6pTDuEKCC5rf&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;e5cdc539-6827-468d-a414-193ab8782021;toolu_vrtx_01RMGdupCPeXgdStxSCfQQCG&quot;:{&quot;requestId&quot;:&quot;e5cdc539-6827-468d-a414-193ab8782021&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01RMGdupCPeXgdStxSCfQQCG&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;bf73faf0-9397-4e26-9257-09a20f5cc3b7;toolu_vrtx_01QjpYdidUYe7y8CGWDCjsXL&quot;:{&quot;requestId&quot;:&quot;bf73faf0-9397-4e26-9257-09a20f5cc3b7&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01QjpYdidUYe7y8CGWDCjsXL&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./EDWA\n./EDWA\\noise_figure.py\n./MFD_setup\n./MFD_setup\\mfd_fit\n./MFD_setup\\polarization_measurement.py\n./OBR_processing\n./OBR_processing\\OBR_rename.py\n./OBR_processing\\cal_delayline_mismatch.py\n./OBR_processing\\obr_data_processing.py\n./OBR_processing\\obr_data_processing_CLEO_plot.py\n./OSA\n./OSA\\OSA_spectrum\n./OSA\\__pycache__\n./OSA\\plot_EDWA_spectrum.py\n./OSA\\plot_QDMLLD_spectrum.py\n./PNA\n./PNA\\__pycache__\n./PNA\\frequency_noise.npy\n./PNA\\offset_frequency.npy\n./PNA\\plot_PN.py\n./PNA\\plot_PN_from_IQ_vectors.py\n./PNA\\plot_RF_phase_noise.py\n./PNA\\plot_RF_spectrum.py\n./PNA\\utils.py\n./utils\n./utils\\Lorentzian_linewidth.py\n./utils\\__init__.py\n./utils\\__pycache__\n./utils\\formatting.py\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;396d0f44-2c70-47cb-8ded-29c515502bb5;toolu_vrtx_01SpYmfRjPZfCwzaoGbnPYLA&quot;:{&quot;requestId&quot;:&quot;396d0f44-2c70-47cb-8ded-29c515502bb5&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01SpYmfRjPZfCwzaoGbnPYLA&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;9b57ec47-d3b8-45b0-8122-ce6f3aab1518;toolu_vrtx_01SUkeFKj95MeWCrnWJhSdcg&quot;:{&quot;requestId&quot;:&quot;9b57ec47-d3b8-45b0-8122-ce6f3aab1518&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01SUkeFKj95MeWCrnWJhSdcg&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Note: Path was automatically corrected from 'PNA/plot_PN.py' to 'C:\\Users\\<USER>\\Documents\\PycharmProject\\process_measurement\\PNA\\plot_PN.py'.\n\nHere's the result of running `cat -n` on C:\\Users\\<USER>\\Documents\\PycharmProject\\process_measurement\\PNA\\plot_PN.py:\n     1\timport os.path\n     2\timport numpy as np\n     3\timport matplotlib\n     4\timport matplotlib.pyplot as plt\n     5\timport glob\n     6\tfrom scipy.constants import c\n     7\t\n     8\tfrom utils import read_csv, get_average_values_from_dir\n     9\tfrom process_measurement.utils.formatting import define_scale, format_large_number\n    10\tplt.rcParams['font.family'] = 'arial'\n    11\tplt.rcParams['font.size'] = 7\n    12\tplt.rcParams['savefig.dpi'] = 300\n    13\t\n    14\t\n    15\tdef plot_freq_noise(x_list, y_list, labels, colors, title='', font_size=14, saving_dir=None):\n    16\t    color_new = matplotlib.colormaps['Paired'].colors\n    17\t\n    18\t    fig, ax = plt.subplots(figsize=(2.3, 1.65))\n    19\t\n    20\t    # Free running from IQ vector\n    21\t    freq = np.load('offset_frequency.npy')\n    22\t    noise = np.load('frequency_noise.npy')\n    23\t    ax.plot(freq, noise, '-', alpha=1, color='tab:pink', linewidth=0.7, label='w/o feedback, 1533 nm')\n    24\t\n    25\t    for i, (x_values, y_values, label, color) in enumerate(zip(x_list, y_list, labels, colors)):\n    26\t        y_values **= 2\n    27\t        ax.plot(x_values, y_values, '-', alpha=1, color=color, label=label, linewidth=0.7)\n    28\t\n    29\t    ax.set_xscale('log')\n    30\t    ax.set_yscale('log')\n    31\t\n    32\t    title = title if title else 'Optical frequency noise'\n    33\t    # ax.set_title(title, fontsize=font_size+2)\n    34\t    ax.set_xlabel(f'Frequency offset [Hz]')\n    35\t    # ax.set_ylabel(r'Frequency noise [Hz$^2$/Hz]')\n    36\t    ax.set_ylabel(r'$S_F$($f$) [Hz$^2$/Hz]')\n    37\t    ax.tick_params(axis='both', direction='in', which='both')\n    38\t\n    39\t    ax.set_xlim([10**2, 10**7])\n    40\t    ax.set_ylim([10**3, 10**18])\n    41\t    ax.set_xticks([10**2, 10**3, 10**4, 10**5, 10**6, 10**7])\n    42\t    ax.set_yticks([10**16, 10**13, 10**10, 10**7, 10**4])\n    43\t    ax.grid(True, which=\&quot;both\&quot;, ls=\&quot;--\&quot;, alpha=0.3)\n    44\t    # ax.axhline(y=10**7, color='r', linewidth=0.5, linestyle='--')\n    45\t    # plt.legend(frameon=False, loc='upper right', handlelength=1, fontsize=5.2, bbox_to_anchor=(1.02, 1.02))\n    46\t    plt.tight_layout()\n    47\t    ax.xaxis.set_label_coords(.5, -0.16)\n    48\t    ax.yaxis.set_label_coords(-0.15, 0.5)\n    49\t    if saving_dir:\n    50\t        print(f'Saved to {os.path.join(saving_dir, title + \&quot;.png\&quot;)}')\n    51\t        fig.savefig(fname=os.path.join(saving_dir, title + '.png'), dpi=1200, transparent=True)\n    52\t    plt.show()\n    53\t\n    54\t\n    55\tdef plot_single_freq_noise(x_values, y_values, title='', save_filename=None, font_size=14, saving_dir=None, show_plot=True):\n    56\t    fig, ax = plt.subplots(figsize=(8, 5))\n    57\t    y_values **= 2\n    58\t    ax.plot(x_values, y_values, '-', alpha=0.8, linewidth=2)\n    59\t\n    60\t    ax.set_xscale('log')\n    61\t    ax.set_yscale('log')\n    62\t\n    63\t    title = title if title else 'Optical frequency noise'\n    64\t    ax.set_title(title, fontsize=font_size+2)\n    65\t    ax.set_xlabel(f'Frequency offset (Hz)', fontsize=font_size)\n    66\t    ax.set_ylabel(r'Frequency noise (Hz/$\\sqrt{Hz}$)', fontsize=font_size)\n    67\t    ax.tick_params(axis='x', labelsize=font_size)\n    68\t    ax.tick_params(axis='y', labelsize=font_size)\n    69\t\n    70\t    ax.set_xlim([min(x_values), max(x_values)])\n    71\t    ax.set_ylim([10e2, 10e10])\n    72\t    ax.grid(True, which=\&quot;both\&quot;, ls=\&quot;--\&quot;, alpha=0.3)\n    73\t    plt.tight_layout()\n    74\t    if saving_dir:\n    75\t        fig.savefig(fname=os.path.join(saving_dir, save_filename if save_filename else title + '.png'), dpi=1200,\n    76\t                    transparent=False)\n    77\t    if show_plot:\n    78\t        plt.show()\n    79\t    plt.clf()\n    80\t\n    81\tdef main():\n    82\t    files = [\n    83\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\locked_spectrum_1527\\225mA 13.5mA_heat_1526.99nm_newport_valid1p93.CSV',\n    84\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\locked_spectrum_1533\\225mA 13.5mA_heat_1532.99nm_newport_valid1p16.CSV',\n    85\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\locked_spectrum_1539\\225mA 13.5mA_heat_1539.01nm_newport_valid1p76.CSV'\n    86\t        ]\n    87\t    labels = ['1527 nm', '1533 nm', '1539 nm']\n    88\t    start_lines = [174, 174, 174]\n    89\t    colors = ['tab:orange', 'red', 'tab:blue', 'tab:purple']\n    90\t\n    91\t    x_list, y_list = [], []\n    92\t    for file, label, start_line in zip(files, labels, start_lines):\n    93\t        x_values, y_values = read_csv(file, start_line=start_line)\n    94\t        x_list.append(x_values)\n    95\t        y_list.append(y_values)\n    96\t\n    97\t    x_list = np.array(x_list)\n    98\t    y_list = np.array(y_list)\n    99\t    plot_freq_noise(x_list, y_list, labels, colors,\n   100\t                    title='Phase noise',\n   101\t                    saving_dir=r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025')\n   102\t\n   103\tdef freq_noise_CLEO():\n   104\t    files = [\n   105\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\07_Optical_linewidth\\Locked\\1527p14\\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p7mA_newfocus1527p14nm_rec4.csv',\n   106\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\07_Optical_linewidth\\Locked\\1533\\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p9mA_rec5.csv',\n   107\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\07_Optical_linewidth\\Locked\\1539p96\\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p9mA_newfocus1539p96nm_rec1.csv'\n   108\t    ]\n   109\t    labels = ['w/ feedback, 1527 nm', 'w/ feedback, 1533 nm', 'w/ feedback, 1539 nm']\n   110\t    start_lines = [184, 184, 184]\n   111\t    colors = ['red', 'green', 'blue']\n   112\t\n   113\t    x_list, y_list = [], []\n   114\t    for file, label, start_line in zip(files, labels, start_lines):\n   115\t        x_values, y_values = read_csv(file, start_line=start_line)\n   116\t        x_list.append(x_values)\n   117\t        y_list.append(y_values)\n   118\t\n   119\t    x_list = np.asarray(x_list)\n   120\t    y_list = np.asarray(y_list)\n   121\t    plot_freq_noise(x_list, y_list, labels, colors,\n   122\t                    title='Phase noise',\n   123\t                    font_size=22, saving_dir=r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\07_Optical_linewidth')\n   124\t\n   125\tdef plot_single_csv_file(file_path, start_line, nr_points, title=None, save_to_folder=None, save_filename=None, show_plot=True):\n   126\t    x_values, y_values = read_csv(file_path, start_line=start_line, nr_points=nr_points)\n   127\t    # x_values, y_values = get_average_values_from_dir(folder, start_line=start_line, nr_points=nr_points, delimiter='\\t')\n   128\t    plot_single_freq_noise(x_values, y_values, title='Phase noise' if title is None else title, save_filename=save_filename,\n   129\t                           font_size=22, saving_dir=save_to_folder, show_plot=show_plot)\n   130\t\n   131\tdef estimate_intrinsic_linewidth(freq_Hz, PSD_Hz_per_sqrt_Hz, f_min=1e6, f_max=10e6, show_plot=False, plot_title=None):\n   132\t    S_nu = PSD_Hz_per_sqrt_Hz ** 2  # in Hz^2/Hz\n   133\t\n   134\t    # Mask the region for white noise floor\n   135\t    mask = (freq_Hz &gt;= f_min) &amp; (freq_Hz &lt;= f_max)\n   136\t    S_white = S_nu[mask]\n   137\t\n   138\t    # Step 3: Estimate white noise level (average)\n   139\t    # f_white = freq_Hz[mask]\n   140\t    S_white_avg = np.median(S_white)\n   141\t\n   142\t    # Step 4: Calculate intrinsic linewidth\n   143\t    delta_nu_intrinsic = np.pi * S_white_avg  # Hz\n   144\t\n   145\t    # Step 5: Print result\n   146\t    print(f\&quot;Estimated intrinsic linewidth: {delta_nu_intrinsic/1e3:.2f} kHz\&quot;)\n   147\t    #\n   148\t    # from sklearn.linear_model import HuberRegressor\n   149\t    # # Fit flat line (slope ≈ 0)\n   150\t    # X = np.log10(f_white).reshape(-1, 1)\n   151\t    # y = np.log10(S_white)\n   152\t    # model = HuberRegressor().fit(X, y)\n   153\t    # log_S_fit = model.predict(X)\n   154\t    # S_white_fit = 10 ** np.mean(log_S_fit)\n   155\t    # delta_nu_fit = np.pi * S_white_fit\n   156\t    # print(f\&quot;Estimated intrinsic linewidth by Huberregressor: {delta_nu_fit}\&quot;)\n   157\t\n   158\t    if show_plot:\n   159\t        # Optional: Plot for verification\n   160\t        plt.loglog(freq_Hz, S_nu, label=\&quot;Sν(f) [Hz²/Hz]\&quot;)\n   161\t        plt.axhline(S_white_avg, color='r', linestyle='--', label='White noise level')\n   162\t        # plt.axhline(S_white_fit, color='b', linestyle='--', label='White noise level - Huber regressor')\n   163\t        plt.axvspan(f_min, f_max, color='gray', alpha=0.2, label='White noise region')\n   164\t        plt.xlabel(\&quot;Frequency offset (Hz)\&quot;)\n   165\t        plt.ylabel(\&quot;Frequency noise PSD (Hz²/Hz)\&quot;)\n   166\t        plt.legend()\n   167\t        plt.title(\&quot;Frequency Noise PSD and White Noise Estimate\&quot; if plot_title is None else plot_title)\n   168\t        plt.grid(True, which='both')\n   169\t        plt.show()\n   170\t    return delta_nu_intrinsic\n   171\t\n   172\tdef plot_folder(folder_path, start_line=174, nr_points=1201, save_to_folder=None):\n   173\t    csv_files = glob.glob(os.path.join(folder_path, '*.csv'), recursive=False)\n   174\t    if not os.path.isdir(save_to_folder):\n   175\t        os.mkdir(save_to_folder)\n   176\t    for csv_file in csv_files:\n   177\t        basename = os.path.basename(csv_file)\n   178\t        file_name_list = basename.split('_')\n   179\t        plot_title = f\&quot;Phase noise of {file_name_list[1]}-{file_name_list[-1][:-len('.csv')]}\&quot;\n   180\t        plot_single_csv_file(file_path=csv_file, start_line=start_line, nr_points=nr_points, title=plot_title,\n   181\t                             save_filename=basename[:-len('.csv')], save_to_folder=save_to_folder, show_plot=False)\n   182\t\n   183\tdef extract_channel_nr_from_filename(filename):\n   184\t    basename = os.path.basename(filename)\n   185\t    channel_nr = basename.split('_')[1][2:]\n   186\t    return int(channel_nr)\n   187\t\n   188\tdef plot_intrinsic_linewidth_over_channel(data_folder, start_line=174, nr_points=1201, save_fig=False):\n   189\t    channel_freq_mapping = {1: 194.4660, 2: 194.5650, 3: 194.6652, 4: 194.7640, 5: 194.8640, 6: 194.9630, 7: 195.0630,\n   190\t                            8: 195.1620, 9: 195.2610, 10: 195.3610, 11: 195.4600, 12: 195.5600, 13: 195.6590, 14: 195.7590}\n   191\t    channels = []\n   192\t    channel_linewidth = []\n   193\t    file_list = glob.glob(os.path.join(data_folder, '*.csv'), recursive=False)\n   194\t    for csv_file in file_list:\n   195\t        x_values, y_values = read_csv(csv_file, start_line=start_line, nr_points=nr_points)\n   196\t        ch_id = extract_channel_nr_from_filename(csv_file)\n   197\t        print(f\&quot;Channel: {ch_id}\&quot;)\n   198\t        channels.append(ch_id)\n   199\t        channel_linewidth.append(estimate_intrinsic_linewidth(freq_Hz=x_values, PSD_Hz_per_sqrt_Hz=y_values,\n   200\t                                                              f_min=1e6, f_max=10e6, show_plot=False, plot_title=f'FN of channel {ch_id}'))\n   201\t    freqs = [channel_freq_mapping[ch] for ch in channels]\n   202\t    fig, ax = plt.subplots(figsize=(3, 1.5))\n   203\t\n   204\t    ax.plot(freqs, np.asarray(channel_linewidth)/1e3, 'o', markersize=3)\n   205\t    ax.set_xlabel('Frequency [THz]')\n   206\t\n   207\t    # ax.plot(c/(np.asarray(freqs)*1e12)*1e9, np.asarray(channel_linewidth)/1e3, 'o', makersize=5)\n   208\t    # ax.set_xlabel('Wavelength [nm]')\n   209\t\n   210\t    ax.set_ylabel('Intrinsic linewidth [kHz]')\n   211\t    ax.set_ylim([30, 140])\n   212\t    ax.grid(True, which='major')\n   213\t\n   214\t    plt.tight_layout()\n   215\t    if save_fig:\n   216\t        plt.savefig(os.path.join(data_folder, 'IntrLinwidth_vs_freq.png'), transparent=False, dpi=300)\n   217\t    plt.show()\n   218\t\n   219\t\n   220\tdef compare_method():\n   221\t    files = [\n   222\t        r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025\\locked_spectrum_1527\\225mA 13.5mA_heat_1526.99nm_newport_valid1p93.CSV',\n   223\t        ]\n   224\t    labels = ['1527 nm PN', '1527 nm IQ']\n   225\t    start_lines = [174, 174, 174]\n   226\t    colors = ['tab:orange', 'red', 'tab:blue', 'tab:purple']\n   227\t\n   228\t    x_list, y_list = [], []\n   229\t\n   230\t    for file, label, start_line in zip(files, labels, start_lines):\n   231\t        x_values, y_values = read_csv(file, start_line=start_line)\n   232\t        x_list.append(x_values)\n   233\t        y_list.append(y_values)\n   234\t\n   235\t    x_list = np.array(x_list)\n   236\t    y_list = np.array(y_list)\n   237\t    plot_freq_noise(x_list, y_list, labels, colors,\n   238\t                    title='Phase noise',\n   239\t                    font_size=22, saving_dir=r'D:\\WORK\\02_Research\\09_Hybrid_comb_packaging\\CLEO2025')\n   240\t\n   241\t\n   242\tif __name__ == '__main__':\n   243\t    # compare_method()\n   244\t    # plot_single_csv_file()\n   245\t    # freq_noise_CLEO()\n   246\t    # plot_folder(folder_path=r'R:\\Bao\\Hybrid_comb\\20250630_locking_FN',\n   247\t    #             start_line=174, nr_points=1201,\n   248\t    #             save_to_folder=r'R:\\Bao\\Hybrid_comb\\20250630_locking_FN\\plots')\n   249\t    # file_path = r'R:\\Bao\\Hybrid_comb\\20250630_locking_FN\\FN_1525p0_QDMLLD_locked_LaserCurrent_250mA_HeaterCurrent_12mA_laserTemp30deg_chipTemp30deg_afterWS_2.csv'\n   250\t    # x_values, y_values = read_csv(file_path, start_line=174, nr_points=1201)\n   251\t    # estimate_intrinsic_linewidth(freq_Hz=x_values, PSD_Hz_per_sqrt_Hz=y_values, show_plot=True)\n   252\t\n   253\t    plot_intrinsic_linewidth_over_channel(data_folder = r'R:\\Bao\\Hybrid_comb\\20250630_locking_FN\\fit_linewidth',\n   254\t                                          save_fig=True)\nTotal lines in file: 254\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;953ed464-e44f-4049-b16b-6d4a156104bb;toolu_vrtx_01PihfNFgAL9ap6TkWyvoJiS&quot;:{&quot;requestId&quot;:&quot;953ed464-e44f-4049-b16b-6d4a156104bb&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01PihfNFgAL9ap6TkWyvoJiS&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Note: Path was automatically corrected from 'PNA/plot_PN.py' to 'C:\\Users\\<USER>\\Documents\\PycharmProject\\process_measurement\\PNA\\plot_PN.py'.\n\nSuccessfully edited the file C:\\Users\\<USER>\\Documents\\PycharmProject\\process_measurement\\PNA\\plot_PN.py.\nResult for str_replace for entry with index [0]:\nReplacement successful.\nnew_str starts at line 210 and ends at line 214.\n\nReview the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).\nEdit the file again if necessary.\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;db17f031-d2ff-4ffb-a914-f2b3d94ed134&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>