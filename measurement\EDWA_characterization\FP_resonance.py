import numpy as np
import matplotlib.pyplot as plt
from scipy.constants import c


# Constants
L = 5e-3  # cavity length in meters
n = 2.0   # refractive index
R = 0.05   # mirror reflectivity
T = 1 - R  # assume no scattering losses
alpha = 2  # internal loss per round trip (set to zero for ideal cavity)
eta = 0.8  # scaling factor (assume ideal coupling)

# Define frequency range around resonance
nu_0 = c / (1550e-9)  # center frequency (e.g. 1550 nm)
delta_nu = 15e9       # +/- 15 GHz around resonance
nu = np.linspace(nu_0 - delta_nu, nu_0 + delta_nu, 1000)

# Compute round-trip phase
phi = 4 * np.pi * n * L * nu / c

# Compute transmission
F = R * np.exp(-alpha)
numerator = eta * T**2 * np.exp(-alpha)
denominator = (1 - F)**2 + 4 * F * np.sin(phi / 2)**2
transmission = numerator / denominator

# Plot
plt.figure(figsize=(8, 4))
plt.plot((nu - nu_0)/1e9, transmission)
plt.xlabel('Frequency offset from resonance (GHz)')
plt.ylabel('$I_t / I_0$')
plt.title('Fabry-Perot Cavity Transmission Spectrum')
plt.grid(True)
plt.tight_layout()
plt.show()
