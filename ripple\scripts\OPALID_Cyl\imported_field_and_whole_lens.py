import matplotlib.pyplot as plt
import numpy as np
import os
import scipy

from ripple.field import Transverse<PERSON><PERSON>
from ripple.field_distribution import GaussianDistribution, ImportedCSTFieldDistribution
from ripple.coupling_optimizer import CouplingScene
from ripple.monitors import FieldMonitor
from ripple.structures import ImportedModel
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_dir
from ripple.utils.export_data import plot_field_grid_far_field


global_x_shift = -25

# lens = ImportedModel(model_file_path=r'./input/FullLens_v2.stl', refractive_index=1.53)
lens = ImportedModel(model_file_path=r'./input/Wedge15deg.stl', refractive_index=1.53)
# lens.rotate(theta_x_deg=180)
lens.translate(tx=global_x_shift, ty=0., tz=0.)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None,
                          record_power_per_z=False, record_beam_radius_per_z=False)

# phase_array = scipy.io.loadmat(r'./input/phiPs_v6.mat')['phiPS']
# phase_array = phase_array.T
phase_array = np.zeros((1, 8))

save_dir = fr'/home/<USER>/sneaky/data'
if not os.path.isdir(save_dir):
    os.makedirs(save_dir)

# ch_positions = np.arange(start=-28, stop=0 + 4., step=4.)[::-1]
ch_positions = np.arange(start=0, stop=28 + 4., step=4.)

if False:
    for i, phase_list in enumerate(phase_array):    # sweeping angles: -20°:2.5°:20°
        phase_list = phase_list[::-1]
        field_list = []
        for ch_pos, ch_phase in zip(ch_positions, phase_list):
            print('ch pos, phase (deg):', ch_pos, ch_phase)
            # TODO: allow copying and shift to build the field faster
            distr = ImportedCSTFieldDistribution(cst_export_file_path=r'./input/e-field (f=193.75) [1]_subVolume.txt',
                                              cut_axis='z', rough_cut_pos=20.05, skiprows=2,
                                              # Nix=None, Niy=None, phase_rad=0)
                                              Nix=None, Niy=None, phase_rad=np.deg2rad(ch_phase))
            ch_field = TransverseField(distribution=distr, z0=0, refractive_index=1.53, wavelength=1.55)
            ch_field.translate_field_xy(delta_x=ch_pos + global_x_shift)
            ch_field.rotate_field_xy(theta_y_deg=15)
            field_list.append(ch_field)
        field_list[0].combine(field_list[1:])
        source_field = field_list[0]

        monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None,
                                  record_power_per_z=False, record_beam_radius_per_z=False)

        scene = CouplingScene(input_field=source_field, target_field=None,
                              background_material_index=1.,
                              sim_z_end=100,
                              optical_structures=[lens],
                              sim_size_xy=[250, 250],
                              wavelength_sampling_xy=8,
                              material_wavelength_sampling_z=8,
                              background_wavelength_sampling_z=4,
                              monitors=[monitor_xz],
                              solver='wpm', boundary_condition='ABC', boundary_parameters=None)
        # source_field.view()
        # scene.preview(mode='xz', position=0)
        # plt.show()

        run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=False,
                name='optimized', sim_result_dir=save_dir, opt_log_dir=None, show_plot=True)

        monitor = FieldMonitor.load(path=os.path.join(save_dir, '1_monitor_xy_optimized.pkl'))
        plot_field_grid_far_field(scene.monitors[-1], folder_path=save_dir, prefix=f'{i}',
                                  save_figure=True , save_data=True, show_plot=False)
        plt.show()


monitor = FieldMonitor.load(path=os.path.join(save_dir, '1_monitor_xy_optimized.pkl'))
plot_field_grid_far_field(monitor, folder_path=save_dir, prefix=f'00',
                          save_figure=True , save_data=True, show_plot=False)
plt.show()