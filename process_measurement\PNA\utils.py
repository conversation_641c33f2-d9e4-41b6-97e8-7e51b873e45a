import csv
import os
import glob
import numpy as np
from itertools import islice
import matplotlib.pyplot as plt
import matplotlib.mlab as mlab
import matplotlib


def read_csv(file_path, start_line=144, nr_points=1201, delimiter='\t'):
    """start_line counts from 0."""
    x_values = []
    y_values = []

    with open(file_path, 'r') as csvfile:
        csvreader = csv.reader(csvfile, delimiter=delimiter)
        for row in islice(csvreader, start_line, start_line + nr_points):  # skip header
            x_values.append(float(row[0]))
            y_values.append(float(row[1]))

    return np.asarray(x_values), np.asarray(y_values)

def get_average_values_from_dir(data_folder, start_line=144, nr_points=1201, delimiter='\t', data_process_lambda=None):
    all_files = glob.glob(os.path.join(data_folder, '*.csv'), recursive=False)
    x_values = None
    y_sum = np.zeros(nr_points)
    for data_file in all_files:
        x_values, y_values = read_csv(data_file, start_line=start_line, nr_points=nr_points, delimiter=delimiter)
        if x_values is not None:
            assert x_values.all() == x_values.all()
        if data_process_lambda is not None:
            assert callable(data_process_lambda), 'data_process_lambda must be callable.'
            x_values, y_values = data_process_lambda(x_values, y_values)
        y_sum += y_values

    return x_values, y_sum/len(all_files)


def cal_timing_jitter(ssb_x_hz, ssb_y_dbc_per_hz, fsr_hz, f_start=None, f_end=None):
    from scipy.integrate import simps
    """
        Calculate timing jitter from SSB phase noise data.

        Parameters:
        ssb_x (ndarray): Offset frequencies in Hz
        ssb_y (ndarray): SSB phase noise in dBc/Hz
        fsr_hz (float): Free Spectral Range (comb repetition rate) in Hz
        f_start, f_end (float): Frequency integration range in Hz
        Returns:
        float: RMS timing jitter in seconds
        """
    if f_start is None:
        f_start = ssb_x_hz.min()
    if f_end is None:
        f_end = ssb_x_hz.max()
    mask = (ssb_x_hz >= f_start) & (ssb_x_hz <= f_end)
    x_filtered = ssb_x_hz[mask]
    y_filtered = ssb_y_dbc_per_hz[mask]

    # Convert dBc/Hz to linear
    L_lin = 10 ** (y_filtered / 10)

    # Integrate phase noise to get phase variance
    sigma_phi_sq = 2 * simps(L_lin, x_filtered)  # Factor 2 for SSB

    # Convert phase variance to timing jitter
    sigma_t = np.sqrt(sigma_phi_sq) / (2 * np.pi * fsr_hz)

    return sigma_t

def cal_freq_noise_from_IQ(I_values, Q_values, sampling_rate=12500000., tau=1/13.536e6, method='mlab_psd'):
    nr_samples = len(I_values)
    angles = np.unwrap(np.arctan2(Q_values, I_values))
    if method == 'self':
        bin_size = sampling_rate/nr_samples
        freq = np.linspace(-(nr_samples-1)/2, (nr_samples-1)/2, num=nr_samples) * bin_size
        pos_indices = freq >= 0
        ft_angle = np.fft.fftshift(np.fft.fft(angles))
        transfer_func = 4 * np.sin(2 * np.pi * freq * tau/2) ** 2
        result = 2 * freq ** 2 * (np.abs(ft_angle) ** 2 / nr_samples / sampling_rate) / transfer_func
        return freq[pos_indices], result[pos_indices]

    elif method == 'mlab_psd':
        fig, ax = plt.subplots()    # cheating
        pxx, freqs = ax.psd(angles, NFFT=nr_samples, Fs=sampling_rate, window=mlab.window_none, pad_to=None,
                            scale_by_freq=True)
        plt.clf()
        plt.cla()
        plt.close()
        transfer_func = 4 * np.sin(2 * np.pi * freqs * tau / 2) ** 2
        result = freqs ** 2 * pxx / transfer_func
        return freqs, result
    else:
        raise ValueError('Method must be self or mlab_psd.')

def estimate_intrinsic_linewidth(freq_Hz, PSD_Hz_per_sqrt_Hz, f_min=1e6, f_max=10e6, show_plot=False, plot_title=None):
    S_nu = PSD_Hz_per_sqrt_Hz ** 2  # in Hz^2/Hz

    # Mask the region for white noise floor
    mask = (freq_Hz >= f_min) & (freq_Hz <= f_max)
    S_white = S_nu[mask]
    S_white_median = np.median(S_white)

    delta_nu_intrinsic = np.pi * S_white_median  # Hz
    # print(f"Estimated intrinsic linewidth: {delta_nu_intrinsic/1e3:.2f} kHz")

    if show_plot:
        # Optional: Plot for verification
        plt.loglog(freq_Hz, S_nu, label="Sν(f) [Hz²/Hz]")
        plt.axhline(S_white_median, color='r', linestyle='--', label='White noise level')
        # plt.axhline(S_white_fit, color='b', linestyle='--', label='White noise level - Huber regressor')
        plt.axvspan(f_min, f_max, color='gray', alpha=0.2, label='White noise region')
        plt.xlabel("Frequency offset (Hz)")
        plt.ylabel("Frequency noise PSD (Hz²/Hz)")
        plt.legend()
        plt.title("Frequency Noise PSD and White Noise Estimate" if plot_title is None else plot_title)
        plt.grid(True, which='both')
        plt.show()
    return delta_nu_intrinsic

def plot_single_frequency_noise(freq, noise, font_size=16, title='', save_path=None, show_plot=True):
    """
    freq: in Hz
    noise: in Hz^2/Hz
    """
    fig, ax = plt.subplots(figsize=(8, 6))
    ax.loglog(freq, noise)
    ax.set_xlim([min(freq), max(freq)])
    # ax.set_ylim([10e2, 10e10])

    ax.tick_params(axis='x', labelsize=font_size)
    ax.tick_params(axis='y', labelsize=font_size)

    ax.set_ylabel(r'$S_F$($f$) [Hz$^2$/Hz]')
    ax.set_xlabel('Offset frequency (Hz)', fontsize=font_size)
    ax.grid(True, which="both", ls="--", alpha=0.3)
    plt.title(title)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, transparent=True)
    if show_plot:
        plt.show()


def plot_multiple_frequency_noise(freqs, noises, labels, freq_range=None, plt_params=None,
                                  title='', save_dir=None, save_filename='MultipleFrequencyNoise.png'):
    """
    freqs: list of frequencies in Hz
    noises: list of noises in Hz^2/Hz
    """
    assert isinstance(freqs, (list, np.ndarray))
    assert isinstance(noises, (list, np.ndarray))
    assert isinstance(labels, (list, np.ndarray))
    assert len(freqs) == len(noises) == len(labels)

    color_new = matplotlib.colormaps['Paired'].colors

    fig, ax = plt.subplots(figsize=(3, 2))
    for freq, noise, label in zip(freqs, noises, labels):
        ax.loglog(freq, noise, label=label, linewidth=1)

    ax.set_xlim([10**2, 10**7] if freq_range is None else freq_range)
    ax.set_ylim([10**3, 10**18])

    xticks = plt_params.get('xticks', None) if plt_params is not None else [10 ** 2, 10 ** 3, 10 ** 4, 10 ** 5, 10 ** 6, max(freq_range)]
    yticks = plt_params.get('yticks', None) if plt_params is not None else [10 ** 16, 10 ** 13, 10 ** 10, 10 ** 7, 10 ** 4]
    ax.set_xticks(xticks)
    ax.set_yticks(yticks)
    ax.grid(True, which="both", ls="--", alpha=0.3)

    plt.title(title)
    ax.set_ylabel(r'$S_F$($f$) [Hz$^2$/Hz]')
    ax.set_xlabel(f'Frequency offset [Hz]')
    ax.tick_params(axis='both', direction='in', which='both')

    # ax.xaxis.set_label_coords(.5, -0.16)
    # ax.yaxis.set_label_coords(-0.15, 0.5)
    plt.legend(prop={'size': 5})
    plt.tight_layout()
    if save_dir:
        print(f'Saved to {os.path.join(save_dir, save_filename + ".png")}')
        fig.savefig(fname=os.path.join(save_dir, save_filename + '.png'), dpi=300, transparent=True)
    plt.show()
