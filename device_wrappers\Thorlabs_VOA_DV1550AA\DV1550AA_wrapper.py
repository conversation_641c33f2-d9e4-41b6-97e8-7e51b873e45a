import serial
import time
import traceback


class VOAController(object):
    def __init__(self, port='COM3'):
        self.ser = serial.Serial(
            port=port,
            baudrate=115200,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=1,
            xonxoff=False,  # Software flow control off
            rtscts=False,  # Hardware flow control off
            dsrdtr=False   # Hardware flow control off
        )
        # Don't set DTR/RTS manually - let them be controlled by flow control settings
        print(f"[INFO] Connected to VOA at port {port} at 115200 bps.")

        self.wait_time = 0.3

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.ser.close()
        self.ser = None

    def send_command(self, command):
        full_command = command + '\r\n'  # Send CR+LF
        self.ser.write(full_command.encode('ascii'))
        time.sleep(self.wait_time)

    def read_response(self):
        response = self.ser.read_all().decode(errors='ignore')
        return response.strip()

    def send_and_read_response(self, command):
        self.send_command(command)
        return self.read_response()

    def close(self):
        self.disable()
        self.ser.close()
        print("[INFO] VOA connection closed.")

    def continuous_control(self):
        # Interactive CLI
        try:
            while True:
                cmd = input("EDFA> ").strip()
                if cmd.lower() in ['exit', 'quit']:
                    break
                elif cmd == '':
                    continue
                else:
                    try:
                        response = self.send_and_read_response(cmd)
                        print(response or "[No response]")
                    except Exception as e:
                        print(e)
        finally:
            self.close()

    def get_operation_status(self, print_response=True):
        op_stat_dict = {'0': 'Disabled', '1': 'Enabled'}
        op_stat = self.send_and_read_response('VOA:POWer?')
        if print_response:
            print(f'VOA is {op_stat_dict[op_stat]}')
        return op_stat

    def enable(self):
        self.send_command('VOA:POWer: 1')

    def disable(self):
        """When disabled, the instrument will be at maximum attenuation."""
        self.send_command('VOA:POWer: 0')

    def get_operating_mode(self, print_response=True) -> str:
        mode_dict = {'0': 'Calibrated Attenuation', '2': 'Manual'}
        mode_id = self.send_and_read_response('VOA:MODE?')
        if print_response:
            print(f"VOA operating mode: [{mode_id}] {mode_dict[mode_id]}")
        return mode_id

    def set_operating_mode(self, mode_id: (int, str)):
        """0: Calibrated Attenuation,
           2: Manual"""
        op_mode = self.get_operating_mode(print_response=False)
        if op_mode != str(mode_id):
            try:
                self.send_command(f'VOA:MODE: {mode_id}')
            except Exception as e:
                print(f'Failed to set operating mode to {mode_id}, current mode is {op_mode}. {traceback.format_exc(e)}')

    def get_attenuation(self):
        attn = self.send_and_read_response('VOA:ATTenuation?')
        return attn.split('\r')[-1]    # FIXME: hot fix, somehow there are one or two 1\r in the front

    def set_attenuation(self, attenuation_dB):
        """Must be in Calibrated Attenuation operating mode to set the
           Attenuation, otherwise “Err: Incorrect mode for this command”
           will be returned"""
        if self.get_operating_mode(print_response=False) != '0':
            self.set_operating_mode('0')
        self.send_command(f'VOA:ATTenuation: {attenuation_dB:.2f}')
        time.sleep(self.wait_time)

    def get_attenuation_range(self, print_response=True):
        min_attn = self.send_and_read_response('VOA:ATTenuation:MIN?')
        max_attn = self.send_and_read_response('VOA:ATTenuation:MAX?')
        if print_response:
            print(f'Attenuation range: [{min_attn}, {max_attn}] dB')
        return min_attn, max_attn

    def get_set_voltage(self):
        """Get the VOA Set Voltage in mV. Range 0 - 5500 mV.
           0 mV = Maximum attenuation
           5500 mV = Near minimum attenuation"""
        set_v = self.send_and_read_response('VOA:SET:Voltage?')
        return set_v

    def set_voltage(self, voltage_mV):
        """This is the voltage applied to the VOA when the instrument is
           operating in Manual Mode and is Enabled.
            going beyond the minimum or maximum attenuation may cause a non-monotonic optical output response. 
            To maintain a monotonic optical response, the use of the VOA:MONO:MIN? and VOA:MONO:MAX? """
        if  self.get_operating_mode(print_response=False) != '2':  # manual mode
            self.set_operating_mode('2')
        if self.get_operation_status(print_response=False) != '1':  # enabled
            self.enable()
        self.send_command(f'VOA:SET:Voltage: {voltage_mV:.2f}')

    def get_actual_voltage(self):
        """Get the actual voltage value in mV that is being applied to the VOA. Range 0 - 5500 mV.
        When the instrument is Disabled this value will read 0 mV."""
        return self.send_and_read_response('VOA:Voltage?')

    def get_monotonic_voltage_range(self, print_response=True):
        """Gets the minimum/maximum voltage (mV) below/above which monotonic behavior is not guaranteed."""
        min_v = self.send_and_read_response('VOA:MONOtonic:MIN?')
        max_v = self.send_and_read_response('VOA:MONOtonic:MAX?')
        if print_response:
            print(f'Monotonic voltage range: [{min_v}, {max_v}] mV')
        return min_v, max_v

    def get_system_wavelength(self):
        """Returns the currently set system wavelength in nanometers (nm). Returns 0 if no wavelength has been set."""
        return self.send_and_read_response('SYStem:WAVElength?')

    def set_system_wavelength(self, wavelength_nm: int):
        """Sets the wavelength for the instrument when the operating mode is set to Calibrated Attenuation.
        X is entered in nm and must be one of the calibrated wavelengths of the device.
        When changing system wavelength, the instrument will default to maximum attenuation."""
        op_mode = self.get_operating_mode(print_response=False)
        assert op_mode == '0', f"Please set the operating mode to Calibrated Attenuation ('0'). Now {op_mode}"
        cali_wl = self.get_calibrated_wavelength()
        assert wavelength_nm in [int(wl) for wl in cali_wl.split(',')], f'Wavelength must be one of the calibrated wavelengths: {cali_wl}'
        self.send_command(f'SYStem:WAVElength: {wavelength_nm}')
        print(f'[INFO] Default to maximum attenuation when changing system wavelength.')

    def get_calibrated_wavelength(self):
        return self.send_and_read_response('SYStem:CAL:WAVElength?')

    def get_system_error(self, print_response=True):
        err_msg = self.send_and_read_response('SYStem:ERRor?')
        if print_response:
            print(f'System error: {err_msg}')
        return err_msg

    def display_VOA_status(self):
        stat = self.send_and_read_response('VOA:STATus?')
        if isinstance(stat, str):
            stat = int(stat)
        operating_status = stat & 0b001  # Bit 0
        config_status = (stat & 0b010) >> 1  # Bit 1, shifted right
        error_status = (stat & 0b100) >> 2  # Bit 2, shifted right

        operating_msg = "Normal." if operating_status else "Not operating normally."
        config_msg = "Configured." if config_status else "Not Configured."
        error_msg = "Error occurred." if error_status else "No Errors."

        print(f"Operating Status:\t{operating_msg}")
        print(f"Configuration Status:\t{config_msg}")
        print(f"Error Status:\t{error_msg}")


if __name__ == '__main__':
    voa = VOAController(port='COM9')
    # voa.get_system_error(print_response=True)
    voa.get_attenuation_range(print_response=True)
    # the internal loss for this device is about 0.4 dBm
    # voa.set_operating_mode('0') # Calibrated Attenuation
    voa.set_system_wavelength(wavelength_nm=1550)
    voa.set_attenuation(attenuation_dB=1)
    voa.enable()
    attn = voa.get_attenuation()
    print(f'{attn = }')
    voa.display_VOA_status()
    time.sleep(30)

    voa.close()

