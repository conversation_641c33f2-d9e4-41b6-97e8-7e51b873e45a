import csv
import os
import numpy as np
import matplotlib.pyplot as plt
import glob

from process_measurement.utils.formatting import define_scale, format_large_number


def read_csv(file_path):
    x_values = []
    y_values = []

    with open(file_path, 'r') as csvfile:
        csvreader = csv.reader(csvfile)
        for row in csvreader:
            x_values.append(float(row[0]))
            y_values.append(float(row[1]))

    return np.asarray(x_values), np.asarray(y_values)


# def fit_FWHM(x_values, y_values):
# """Not accurate for measurement where the measurement time is long, obsolete"""
#     FWHM_scale = define_scale(x_values)
#     x_values = format_large_number(x_values, scale=FWHM_scale)
#     # TODO: fit lorentzian


def plot_OSA_spectrum(x_values, y_values, title='', font_size=14, saving_dir=None, scale=None, fig_name=None,
                      x_lim=None, y_lim=None):
    scale = define_scale(min(min(x_values), min(x_values))) if scale is None else scale
    x_values = format_large_number(x_values, scale=scale)

    fig, ax = plt.subplots(figsize=(6, 2))
    ax.plot(x_values, y_values, '-', color='tab:blue')
    ax.set_title(title, fontsize=font_size+2)
    ax.set_xlabel(f'Wavelength ({scale}m)', fontsize=font_size)
    ax.set_ylabel('Power (dBm)', fontsize=font_size)
    ax.set_ylim([min(y_values) - 1, max(y_values) + 1] if y_lim is None else y_lim)
    x_lim = [min(x_values), max(x_values)] if x_lim is None else x_lim
    ax.set_xlim(x_lim)
    ax.set_xticks(np.arange(round(min(x_lim)), round(max(x_lim)), 3))
    ax.tick_params(axis='x', labelsize=font_size)
    ax.tick_params(axis='y', labelsize=font_size)
    ax.grid(True)
    plt.tight_layout()
    if saving_dir:
        fig.savefig(fname=os.path.join(saving_dir, (fig_name if fig_name else title) + '.png'), dpi=1200)
    plt.show()


def QDMLLD_beat_with_newport():
    file_dir = r'C:\Users\<USER>\Documents\WORK\02_Research\09_Lenses_design\Hybrid_comb_packaging\04_Characterization\04_Locking_experiment\QDMLLD_49p7G_linewidth_w_lens\QDMLLD_49p7G_w_lens_to_SiN_w_prism_and_lens_linewidth'
    plot_dir = r'C:\Users\<USER>\Documents\WORK\02_Research\09_Lenses_design\Hybrid_comb_packaging\04_Characterization\04_Locking_experiment\QDMLLD_49p7G_linewidth_w_lens\QDMLLD_49p7G_w_lens_to_SiN_w_prism_and_lens_linewidth\plots'

    files = [
        r'REC6_OSA_QDMLLD_current249mA_lens_angled_airgap_prism_SiN_lens_delayline_30cm_locked_DL_17.4mA.csv',
        r'REC7_OSA_QDMLLD_current249mA_lens_angled_airgap_prism_SiN_lens_delayline_30cm_locked_DL_17.4mA_Heterodyn_Newport.csv',
        r'REC8_OSA_QDMLLD_current249mA_lens_angled_airgap_prism_SiN_lens_delayline_30cm_locked_DL_17.4mA_Heterodyn_Newport.csv'
    ]

    file_path = os.path.join(file_dir, files[0])
    x_values, y_values = read_csv(file_path)
    plot_OSA_spectrum(x_values, y_values, font_size=22, title='', saving_dir=plot_dir, scale='n')


def plot_multi_OSA_spectrum(x_list, y_list, labels, title='', font_size=12, saving_dir=None):
    # scale = define_scale(min(min(x_values), min(x_values)))
    plt.rcParams['font.family'] = 'arial'
    plt.rcParams['font.size'] = 12

    fig, ax = plt.subplots(figsize=(4, 3))
    ct = 1
    for x_values, y_values, label in zip(x_list, y_list, labels):
        if ct > 1:
            break
        scale = 'n'
        x_values = format_large_number(x_values, scale=scale)
        # y_values -= np.amax(y_values)     # normalize the power
        ax.plot(x_values, y_values, '-', alpha=0.8, label=label, linewidth=1)
        ct += 1
    # title = title if title else 'OSA Spectrum'
    ax.set_title(title, fontsize=font_size+2)
    ax.set_xlabel(f'Wavelength (nm)', fontsize=font_size)
    ax.set_ylabel('Power (dBm)', fontsize=font_size)
    ax.set_xlim([1510, 1555])
    ax.set_ylim([-65, 0])
    # ax.tick_params(axis='x', labelsize=font_size)
    # ax.tick_params(axis='y', labelsize=font_size)
    # plt.legend(loc='upper right', fontsize=font_size-2)
    ax.grid(True)
    plt.tight_layout()
    if saving_dir:
        fig.savefig(fname=os.path.join(saving_dir, '1.png'), dpi=1200, transparent=False)
    plt.show()


def compare_OSA_spectrum():
    file_dir = r'C:\Users\<USER>\Documents\WORK\02_Research\09_Lenses_design\Hybrid_comb_packaging\04_Characterization\03_QD-MLLD_with_lens\QDMLLD_D1_49p7GHz_OSA_comparison'
    plot_dir = file_dir

    files = [
        # r'00_QDMLLD_OSA_span_100nm_current_100p0mA_laser_TEC_25deg_wo_lens.csv',
        r'QDMLLD_OSA_span_100nm_current_250p0mA_laser_TEC_25deg.csv',
        r'REC1_OSA_QDMLLD_current250mA_lens_angled_airgap_prism.csv', # current lens
        # r'QDMLLD_OSA_span_100nm_current_100p0mA_laser_TEC_25deg_attached_lens_printed.csv',     # with ripple
        # r'QDMLLD_OSA_span_100nm_current_100p0mA_laser_TEC_25deg_lens_airgap_printed.csv',   # previous lens
        r'02_REC6_OSA_QDMLLD_current249mA_lens_angled_airgap_prism_SiN_lens_delayline_30cm_locked_DL_17.4mA_wo_Heterodyn_Newport.csv'
    ]
    labels = ['Without lens', 'With lens', 'With lens and delay line']
    x_list, y_list = [], []
    for file in files:
        file_path = os.path.join(file_dir, file)
        x_values, y_values = read_csv(file_path)
        x_list.append(x_values)
        y_list.append(y_values)
    x_list = np.array(x_list, dtype='object')
    y_list = np.array(y_list, dtype='object')
    plot_multi_OSA_spectrum(x_list, y_list, labels=labels,
                            font_size=16, title='OSA_Spectra_comparison', saving_dir=plot_dir)


def compare_OSA_spectrum_split_plot():
    folder = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\06_OSA_spectrum_locked_and_unlocked'
    file1 = r'OSA_QDMLLD_49p7GHz_LaserCurrent_225mA_free_running_50nm.csv'
    file2 = r'OSA_QDMLLD_49p7GHz_LaserCurrent_225mA_with_SiN_locked_50nm_wo_newfocus_final.csv'

    wl1, power1 = read_csv(os.path.join(folder, file1))
    wl2, power2 = read_csv(os.path.join(folder, file2))

    plt.rcParams['font.family'] = 'arial'
    plt.rcParams['font.size'] = 12

    fig, axes = plt.subplots(2, 1, figsize=(3.5, 3))
    wl1 = format_large_number(wl1, scale='n')
    wl2 = format_large_number(wl2, scale='n')
    axes[0].plot(wl1, power1, '-', alpha=1, linewidth=1, color='tab:blue', label='w/o feedback')
    axes[1].plot(wl2, power2, '-', alpha=1, linewidth=1, color='tab:purple', label='w/ feedback')
    # axes[0].set_title('w/o feedback')
    # axes[1].set_title('w/ feedback')
    for ax in axes:
        ax.set_xlabel('Wavelength [nm]')
        ax.set_ylabel('Power [dBm]')
        ax.set_xlim([wl1[0] + 10, wl1[-1] - 10])
        ax.set_ylim([-60, 1])
        ax.tick_params(axis='x', direction="in")
        ax.tick_params(axis='y', direction="in")
        # ax.xaxis.set_label_coords(.5, -0.45)
        # ax.yaxis.set_label_coords(-0.25, 0.5)
        ax.set_xticks([1520, 1533, 1545])
        ax.set_yticks([0, -30, -60])
        ax.grid(True, alpha=0.5)
        # ax.legend(loc='upper right', bbox_to_anchor=(1.02, 1.1), frameon=False, handlelength=1.2)
    plt.subplots_adjust(hspace=0)
    plt.tight_layout()
    plt.savefig(fname=os.path.join(folder, 'OSA_spectrum.png'), dpi=1200, transparent=True)
    plt.show()


def compare_lenses():
    file_dir = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\06_Characterization\03_QD-MLLD_with_lens\QDMLLD_D1_49p7GHz_OSA_comparison'
    plot_dir = file_dir

    files = [
        # r'00_QDMLLD_OSA_span_100nm_current_100p0mA_laser_TEC_25deg_wo_lens.csv',
        # r'QDMLLD_OSA_span_100nm_current_250p0mA_laser_TEC_25deg.csv',
        # r'01_QDMLLD_OSA_span_100nm_current_100p0mA_laser_TEC_25deg_lens_airgap_printed_optim_design.csv', # current lens
        r'QDMLLD_OSA_span_100nm_current_100p0mA_laser_TEC_25deg_attached_lens_printed.csv',     # with ripple
        r'QDMLLD_OSA_span_100nm_current_100p0mA_laser_TEC_25deg_lens_airgap_printed.csv',  # previous lens
    ]
    labels = ['w/o air gap', 'w/ air gap']

    x_list, y_list = [], []
    for file in files:
        file_path = os.path.join(file_dir, file)
        x_values, y_values = read_csv(file_path)
        x_list.append(x_values)
        y_list.append(y_values)
    x_list = np.array(x_list)
    y_list = np.array(y_list)
    plot_multi_OSA_spectrum(x_list, y_list, labels=labels,
                            font_size=16, title='', saving_dir=plot_dir)

def QDMLLD_journal_plot(file_path):
    x_values, y_values = read_csv(file_path)
    plot_OSA_spectrum(x_values, y_values,
                      x_lim=[1530, 1543],
                      y_lim=[-60, max(y_values) + 5],
                      # title='Modulated Free-running QDMLLD Spectrum',
                      saving_dir=os.path.dirname(file_path), fig_name=os.path.basename(file_path)[:-len('.csv')],
                      font_size=14, scale='n')

def plot_folder(folder_path):
    file_list = glob.glob(os.path.join(folder_path, '*.csv'))
    for csv_file in file_list:
        QDMLLD_journal_plot(csv_file)


if __name__ == "__main__":
    # QDMLLD_beat_with_newport()
    # compare_OSA_spectrum()
    # compare_OSA_spectrum_split_plot()
    # compare_lenses()
    # plot_folder(folder_path=r'R:\Peng\QDMLLD_WDM\20250622_QDMLLD_Locking\20250630_OSA_modulated_sig')

    # foler_path = r'R:\Peng\QDMLLD_WDM\20250622_QDMLLD_Locking\OSA'
    foler_path = r'R:\Peng\QDMLLD_WDM\20250622_QDMLLD_Locking\20250630_OSA_modulated_sig'
    # file_name = r'QDMLLD_freerunning_LaserCurrent_250mA_HeaterCurrent_12mA_laserTemp30deg_chipTemp30deg_14CombTones_afterIQM_EDFA_RBW_0.1nm_Sens_High1_SPAN20nm_pt10001_OB2B_10percent.csv'
    file_name = r'QDMLLD_locked_LaserCurrent_250mA_HeaterCurrent_12mA_laserTemp30deg_chipTemp30deg_14CombTones_afterIQM_EDFA_RBW_0.02nm_Sens_High1_SPAN20nm_pt10001_87km_10percent.csv'
    QDMLLD_journal_plot(os.path.join(foler_path, file_name))
