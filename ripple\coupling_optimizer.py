from __future__ import division

import os.path
import numpy as np
import scipy.optimize
from collections import OrderedDict
from colorama import Fore
import matplotlib.pyplot as plt
from datetime import datetime

from ripple.field import TransverseField
from ripple.field_distribution import GaussianDistribution
from ripple.structures import MaterialMask
from ripple.monitors import FieldMonitor
from ripple.wpms import WavePropagationMethod, FastPolarizedWPM, BidirectionalVectorialWPM
from ripple.utils.ascii_text import title_standard
from ripple.utils.plot import get_ax_labels
from ripple.utils.io import create_file, append_to_file
# FIXME: look into the memory issue -- z slices should be deleted after propagation

# Ref: https://docs.scipy.org/doc/scipy/reference/generated/scipy.optimize.show_options.html#scipy.optimize.show_options
OPT_OPTIONS = {'Nelder-Mead': {'xatol': 1.0,
                              # fatol -- Absolute error between iterations that is acceptable for convergence.
                               'fatol': 0.0001,
                               'adaptive': True},  # Useful for high-dimensional minimization
               'Powell': {'xatol': 1.0,
                          'fatol': 0.0001}}


class CouplingScene(object):
    def __init__(self, input_field, background_material_index,
                 optical_structures,
                 sim_size_xy, sim_z_start=None, sim_z_end=None, cut_exactly_at_z_end=False, target_field=None,
                 force_xy_res=None, force_z_res=None,
                 wavelength_sampling_xy=None, material_wavelength_sampling_z=None,
                 background_wavelength_sampling_z=None,
                 monitors=None, solver='wpm', solver_pars=None,
                 boundary_condition='ABC', boundary_parameters=None, z_margin_ratio=0.05):
        assert isinstance(input_field, TransverseField)
        self.input_field = input_field

        if target_field is not None:
            assert isinstance(target_field, TransverseField)
        self.target_field = target_field

        self.wavelength = self.input_field.wavelength        # TODO: be able to optimize for multiple wavelength
        self.background_n = background_material_index

        optical_structures = [optical_structures] if not isinstance(optical_structures, list) else optical_structures
        # optical_structures = [structure for structure in optical_structures if structure.n != self.background_n]
        for optical_stru in optical_structures:
            assert isinstance(optical_stru, MaterialMask)
        self.optical_structures = optical_structures
        self._named_structures = {structure.name: structure for structure in self.optical_structures}
        self._update_boolean = False

        self.max_n = np.amax([structure.n for structure in optical_structures] + [self.background_n])

        self.sim_z_start = sim_z_start if sim_z_start is not None else self.input_field.z0
        if self.target_field is not None:
            self.z_focus = sim_z_end if sim_z_end is not None else self.target_field.z0  # simulation settings have priority
        else:
            assert sim_z_end is not None, "sim_z_end cannot be None when the target field is not given."
            self.z_focus = sim_z_end
        assert self.z_focus is not None, ('No target field given, please specify simulation size in z direction with '
                                          'sim_z_end.')
        if cut_exactly_at_z_end:
            self.sim_z_end = self.z_focus
        else:
            # z_margin_ratio: to make sure the focal plane will not fall out of sim region due to the change of z_list
            sim_z_end_min = self.z_focus * (1. + z_margin_ratio)
            self.sim_z_end = sim_z_end_min if sim_z_end is None else max(sim_z_end, sim_z_end_min)

        self._z_list = None
        self._interpolated_z = None

        assert isinstance(sim_size_xy, (list, np.ndarray)) and len(sim_size_xy) == 2, \
            "The size of simulation on both x and y direction should be given."
        self.sim_size_xy = np.asarray([sim_size_xy, sim_size_xy]) \
            if sim_size_xy in (int, float) else np.asarray(sim_size_xy)

        wavelength_sampling_xy = self.convert_data_type(wavelength_sampling_xy, replace_none=50)
        if force_xy_res:
            self.target_material_res_xy = self.convert_data_type(force_xy_res)
        else:
            self.target_material_res_xy = self.cal_res(self.wavelength, self.max_n, wavelength_sampling_xy)

        if force_z_res:
            self._force_z_res = True
            self.target_material_res_z = force_z_res
            self.target_background_res_z = force_z_res
        else:
            self._force_z_res = False
            self.target_material_res_z = self.cal_res(self.wavelength, self.max_n, material_wavelength_sampling_z)
            self.target_background_res_z = self.cal_res(self.wavelength, self.background_n,
                                                        background_wavelength_sampling_z)

        if monitors is None:
            self.monitors = []
        else:
            self.monitors = [monitors] if not isinstance(monitors, list) else monitors
            for monitor in self.monitors:
                assert isinstance(monitor, FieldMonitor)
        self.focal_plane_monitor = FieldMonitor(monitor_type='xy', position=self.z_focus, saving_path=None,
                                                name='focus')
        self.monitors.extend([self.focal_plane_monitor])    # assumes the last one to be focal plane monitor
        # TODO: draw target mode field diameter in the focal plane monitor
        self._monitors_to_update = []
        self.cal_slices_power = any([monitor.record_power for monitor in self.monitors])
        self.cal_slices_beam_radius = any([monitor.record_beam_radius for monitor in self.monitors])

        self.boundary_condition = boundary_condition
        default_boundary = {"sim_size": self.sim_size_xy, "boundary_thickness": [3]*2}
        self.boundary_parameters = {'PML': {**default_boundary, "shape": "raised_sinusoidal"},
                                    'ABC': default_boundary
                                    }[self.boundary_condition]
        if boundary_parameters is not None:
            self.boundary_parameters.update(boundary_parameters)

        self._sim_callbacks = self.callbacks()
        self._opt_callbacks = self.opt_callback()

        assert solver in ('wpm', 'fpwpm', 'bvwpm'), f'{solver} is not implemented.'
        self.default_solver_pars = {'wpm': {'use_fresnel_correction': True},
                                    'fpwpm': {'polarization': 'x'},
                                    'bvwpm': {'polarization': 'x', 'iterations': 1}}[solver]
        if solver_pars:
            self.default_solver_pars.update(solver_pars)
        self.selected_solver = {'wpm': WavePropagationMethod,
                                'fpwpm': FastPolarizedWPM,
                                'bvwpm': BidirectionalVectorialWPM}[solver]
        self.solver = self.init_solver()

        self.input_field.calculate_field(x_grid=self.solver.grid_x, y_grid=self.solver.grid_y)

        if self.target_field:
            self.target_field.calculate_field(x_grid=self.solver.grid_x, y_grid=self.solver.grid_y)

        self._meshed_structure = False
        self.setup_monitors()

        self.parameters_to_optimize = None
        self.original_pars_structure = None
        self.current_pars = None
        self.current_coupling = 0
        self.n_iter = 0

        self.best_pars = None
        self.best_coupling = None

        self.log_file = None

        self.original_structure = {}

    @property
    def sim_lims(self):
        return {'x': [-self.sim_size_xy[0] / 2, self.sim_size_xy[0] / 2],
                'y': [-self.sim_size_xy[1] / 2, self.sim_size_xy[1] / 2],
                'z': [self.sim_z_start, self.sim_z_end]}

    @staticmethod
    def convert_data_type(data, replace_none=50):
        s_xy = data
        if s_xy is None:
            return np.array([replace_none, replace_none])
        elif isinstance(s_xy, (int, float)):
            return np.array([s_xy, s_xy])
        elif isinstance(s_xy, (list, np.ndarray)):
            return np.asarray(s_xy)
        else:
            raise TypeError(f"Type {type(data)} not accepted.")

    def sim_size(self, ax):
        assert ax in ('x', 'y', 'z')
        match ax:
            case 'x': return self.solver.nr_grid_x
            case 'y': return self.solver.nr_grid_y
            case 'z': return len(self.interpolated_z) if self.interpolated_z is not None else 1

    def set_optimization_parameters(self, par_values: (list, np.ndarray)):
        par_values = self._restruct_list(par_values)
        new_par = iter(par_values)
        for structure_name, par_dicts in self.parameters_to_optimize.items():
            structure = self._named_structures[structure_name]
            structure.clear_old_attr()
            for surface_name, par_names in par_dicts.get('Surface', {}).items():
                surface = getattr(structure, surface_name)
                for par_name in par_names:
                    setattr(surface, par_name, next(new_par))

            #FIXME: This only works if rotation is done before translation
            temp_r = {'x': 0, 'y': 0, 'z': 0}
            for r_d in par_dicts.get('Rotation', []):
                temp_r[r_d] = next(new_par)
                structure.rotation_angles = temp_r

            temp_t = {'x': 0, 'y': 0, 'z': 0}
            for t_d in par_dicts.get('Translation', []):
                temp_t[t_d] = next(new_par)
                structure.translation_vectors = temp_t

    def get_optimization_parameters(self, accuracy=np.float64):
        par_values = []
        for structure_name, par_dicts in self.parameters_to_optimize.items():
            structure = self._named_structures[structure_name]
            for surface_name, par_names in par_dicts.get('Surface', {}).items():
                surface = getattr(structure, surface_name)
                for par_name in par_names:
                    par_values.extend([getattr(surface, par_name)])
            for r_d in par_dicts.get('Rotation', []):
                par_values.extend([structure.rotation_angles[r_d]])
            for t_d in par_dicts.get('Translation', []):
                par_values.extend([structure.translation_vectors[t_d]])

        if self.n_iter == 0:
            self.original_pars_structure = par_values.copy()
        par_values = self._flatten_list(par_values)
        return np.asarray(par_values, dtype=accuracy)

    @staticmethod
    def _flatten_list(input_list):
        """Flattens a nested list into a single list of integers and floats."""
        def _flatten(sublist):
            for item in sublist:
                if isinstance(item, (list, np.ndarray)):
                    yield from _flatten(item)
                else:
                    yield item
        return list(_flatten(input_list))

    def _restruct_list(self, flat_list):
        """Restructures a flat list back to the original nested format."""
        iter_flat_list = iter(flat_list)
        def _restruct(sublist):
            return [next(iter_flat_list) if not isinstance(item, (list, np.ndarray)) else _restruct(item) for item in sublist]
        return _restruct(self.original_pars_structure)

    def optimization_info(self, parameters=None):
        par_str = ''
        par_list = self.current_pars if parameters is None else parameters
        par_val = iter(self._restruct_list(par_list))
        for structure_name, par_dicts in self.parameters_to_optimize.items():
            par_str += '\n- Structure name: ' + Fore.YELLOW + f'{structure_name}\n' + Fore.RESET + '- Parameters:'
            for surface_name, par_names in par_dicts.get('Surface', {}).items():
                par_str += f'\n\t{surface_name}:\t'
                for par_name in par_names:
                    par_str += Fore.LIGHTGREEN_EX + f'{par_name}: {next(par_val)}\t\t'
                par_str += Fore.RESET

            rotation_str = '\t'.join([f'{par}: {next(par_val)}' for par in par_dicts.get('Rotation', [])])
            par_str += f'\n\trotation: ' + Fore.LIGHTGREEN_EX + rotation_str + Fore.RESET if rotation_str else ''

            translation_str = '\t'.join([f'{par}: {next(par_val)}' for par in par_dicts.get('Translation', [])])
            par_str += f'\n\ttranslation: ' + Fore.LIGHTGREEN_EX + translation_str + Fore.RESET \
                        if translation_str else ''
        return par_str

    def init_solver(self):
        return self.selected_solver(sim_size_xy=self.sim_size_xy, target_res_xy=self.target_material_res_xy,
                                    wavelength=self.wavelength,
                                    optical_structures=self.optical_structures,
                                    env_material_index=self.background_n,
                                    boundary_condition=self.boundary_condition,
                                    boundary_pars=self.boundary_parameters,
                                    on_gpu=True, **self.default_solver_pars)

    def init_structures(self):
        priorities = []
        for structure in self.optical_structures:
            structure.store_grids(x_grid=self.solver.grid_x, y_grid=self.solver.grid_y, r_grid=self.solver.grid_r,
                                  update_xy=True)
            priorities.append(structure.priority)
        if np.diff(priorities).any():
            self._update_boolean = True

    def calculate_masks(self):
        for structure in self.optical_structures:
            structure.calculate_mask_from_z(z_pos=self.interpolated_z, assign_z=True)
        if self._update_boolean:
            MaterialMask.remove_overlap_by_priority(*self.optical_structures)
        # self.solver.structures = self.optical_structures

    def update_scene(self, par_values: (list, np.ndarray) = None, update_structure=False):
        """This only needs to be done at the beginning of the simulation."""
        if update_structure:
            self.set_optimization_parameters(par_values)

        self.update_attached_structures()

        self.calculate_z_locs()
        self.calculate_masks()

        self._update_xy_monitor_position()
        for monitor in self.monitors:
            monitor.reset()
            x_label, y_label, cut = get_ax_labels(mode=monitor.type)
            x, y, masks = self.get_mask_slice(ax0_label=x_label, ax1_label=y_label, cut_label=cut,
                                              cut_position=monitor.position)
            monitor.structure_x_y_mask = (x, y, masks)

    def update_attached_structures(self):
        if self.parameters_to_optimize is None:
            return
        for stru in self.optical_structures:
            if stru.name not in self.parameters_to_optimize.keys():
                continue
            if 'Translation' not in self.parameters_to_optimize[stru.name].keys() or stru.attached_structures is None:
                continue

            current_stru_pos = stru.translation_vectors
            original_stru_pos = stru.original_translation
            delta_x = current_stru_pos['x'] - original_stru_pos['x']
            delta_y = current_stru_pos['y'] - original_stru_pos['y']
            for attached_structure in stru.attached_structures:
                if isinstance(attached_structure, TransverseField):
                    self.update_attached_field_position(attached_structure, delta_x=delta_x, delta_y=delta_y)
                elif isinstance(attached_structure, MaterialMask):
                    self.update_attached_mask(attached_structure, delta_x=delta_x, delta_y=delta_y)

    @staticmethod
    def update_attached_mask(attached_mask, delta_x, delta_y):
        # TODO: support rotation operation
        attached_mask.clear_old_attr()
        original_t = attached_mask.original_translation
        temp_t = {'x': delta_x + original_t['x'], 'y': delta_y + original_t['y'], 'z': original_t['z']}
        attached_mask.translation_vectors = temp_t

    def update_attached_field_position(self, attached_field, delta_x, delta_y):
        # TODO: support rotation operation
        # must update each time, or when delta x/y is 0, the field will not move back to original position
        # also the 'translate' function in field should be updated
        if attached_field.original_translation is None:
            attached_field.original_translation = {'x': 0., 'y': 0.}
        original_field_pos = attached_field.original_translation
        attached_field.translate_field_xy(original_field_pos['x'] + delta_x, original_field_pos['y'] + delta_y)
        attached_field.calculate_field(x_grid=self.solver.grid_x, y_grid=self.solver.grid_y)

    def callbacks(self):
        def _callback(**kwargs):
            for monitor in self.monitors:
                monitor.insert_values(**kwargs)
        return _callback

    def opt_callback(self):
        def _callback(**kwargs):
            self.monitors[-1].insert_values(**kwargs)
        return _callback

    def setup_monitors(self):
        for i, monitor in enumerate(self.monitors):
            monitor.x_values = self.solver.x_values.copy()
            monitor.y_values = self.solver.y_values.copy()
            monitor.wavelength = self.wavelength
            if monitor.type == 'xy':
                self._monitors_to_update.extend([i])
                continue
            elif monitor.type == 'yz':
                monitor.position = 0 if monitor.position is None else monitor.position
                positions = self.solver.x_values.copy()
            elif monitor.type == 'xz':
                monitor.position = 0 if monitor.position is None else monitor.position
                positions = self.solver.y_values.copy()
            else:
                raise NotImplementedError(f'Unknown minitor type{monitor.type}')

            assert (monitor.position >= positions[0]) and (monitor.position <= positions[-1]), \
                (f'{monitor.type} monitor position {monitor.position} out of simulation range '
                 f'{[positions[0], positions[-1]]}')
            monitor.get_position_id(positions)

    def _update_xy_monitor_position(self):
        positions = self.interpolated_z
        for i in self._monitors_to_update:
            self.monitors[i].get_position_id(positions)

    @property
    def interpolated_z(self):
        if self._interpolated_z is None:
            self.calculate_z_locs()
        return self._interpolated_z

    @property
    def z_list(self):
        if self._z_list is None:
            self.calculate_z_locs()
        return self._z_list

    def calculate_z_locs(self):
        if len(self.optical_structures) == 0 or self._force_z_res:
            self._z_list = np.arange(start=self.sim_z_start, stop=self.sim_z_end, step=self.target_background_res_z)
            self._interpolated_z = self.solver.interpolate_z(self.z_list)
            return 0

        structure_boundaries = (np.asarray([[max(0, stru.z_min), min(self.sim_z_end, stru.z_max)]
                                            for stru in self.optical_structures]).flatten())

        diff = np.diff(structure_boundaries)
        overlap = np.where(diff < 0)[0]
        nonoverlap_boundaries = np.delete(structure_boundaries, np.concatenate([overlap, overlap+1])).reshape(-1, 2)

        z_values = []
        z0 = self.sim_z_start
        z_left, z_right = z0, self.sim_z_end
        for boundary in nonoverlap_boundaries:
            z_left = boundary[0] if boundary[0] >= z0 else z0
            z_right = boundary[1] if boundary[1] < self.sim_z_end else self.sim_z_end
            z_values.extend(np.arange(start=z0, stop=z_left,
                                      step=self.target_background_res_z))
            z_values.extend(np.arange(start=z_left, stop=z_right + self.target_material_res_z,
                                      step=self.target_material_res_z))
            z0 = boundary[1]
        if z_right <= self.sim_z_end:
            z_values.extend(np.arange(start=z_right, stop=self.sim_z_end + self.target_background_res_z,
                                      step=self.target_background_res_z))
        z_values.extend([self.sim_z_end])
        self._z_list = np.unique(np.asarray(z_values))
        self._interpolated_z = self.solver.interpolate_z(self.z_list)

    @staticmethod
    def cal_res(wavelength, material_index, oversampling):
        return wavelength / material_index / oversampling

    def view_surfaces(self):
        fig, ax = plt.subplots(subplot_kw={"projection": "3d"})
        for structure in self.optical_structures:
            structure.view_3D_surfaces(ax=ax)
        ax.set_xlabel('z')
        ax.set_ylabel('y')
        ax.set_zlabel('z')
        plt.show()

    def view_3D_mesh(self):
        import plotly.graph_objects as go

        if self.interpolated_z is None:
            self.calculate_z_locs()

        masks = np.zeros(shape=(self.sim_size('z'), self.sim_size('x'), self.sim_size('y')))
        for structure in self.optical_structures:
            if structure.mask is None:
                structure.get_mask_at_z(z_pos=self.interpolated_z)
            masks += structure.n * structure.mask
        X = np.repeat([self.optical_structures[0].x_grid_original], len(self.interpolated_z), axis=0)
        Y = np.repeat([self.optical_structures[0].y_grid_original], len(self.interpolated_z), axis=0)
        Z = np.asarray([np.ones(shape=self.optical_structures[0].x_grid_original.shape) * z
                        for z in self.optical_structures])
        fig = go.Figure(data=go.Volume(
            x=X.flatten(),
            y=Y.flatten(),
            z=Z.flatten(),
            value=masks.astype(int).flatten(),
            isomin=0,
            isomax=1.1,
            opacity=0.1,  # needs to be small to see through all surfaces
            # surface_count=21,
            colorscale='blues'
        ))
        fig.show()

    def get_mask_slice(self, ax0_label, ax1_label, cut_label, cut_position: float):
        masks_filled = [stru.mask is not None for stru in self.optical_structures]
        if masks_filled and all(masks_filled):   # if the masks in a 3D volume are already calculated, just get a slice
            masks = np.ones(shape=(self.sim_size(ax0_label), self.sim_size(ax1_label))) * self.background_n
            x, y = None, None
            for structure in self.optical_structures:
                x, y, mask = structure.slice_mask(x_label=ax0_label, y_label=ax1_label, cut_label=cut_label,
                                                  cut_position=cut_position)
                masks[mask.astype(np.bool_)] = structure.n
            return x, y, masks

        x, y = np.asarray(self.solver.grid_x[:, 0]), np.asarray(self.solver.grid_y[0, :])
        if cut_label == 'z':
            if self._z_list is None:
                self._z_list = np.asarray([cut_position])
                self._interpolated_z = np.asarray([cut_position])
            masks = np.ones(shape=(self.sim_size('x'), self.sim_size('y'))) * self.background_n
            self.init_structures()
            for structure in self.optical_structures:
                structure.get_mask_at_z(z=cut_position)
            MaterialMask.remove_overlap_by_priority(*self.optical_structures)
            for structure in self.optical_structures:
                masks[structure.mask[0].astype(np.bool_)] = structure.n
                structure.clear_old_attr(deep=True)  # remove partially calculated masks from preview
            return x, y, masks

        assert ax0_label == 'z', 'z axis is assumed to be the plot x axis'
        ax_plt = {'x': x, 'y': y}
        ax_plt['xy'.replace(ax1_label, '')] = np.array([float(cut_position)])
        for structure in self.optical_structures:
            structure.store_grids(x_grid=ax_plt['x'], y_grid=ax_plt['y'], update_xy=True)

        if self.interpolated_z is None:
            self.calculate_z_locs()

        masks = np.ones(shape=(self.sim_size(ax0_label), self.sim_size(ax1_label))) * self.background_n
        for structure in self.optical_structures:
            structure.calculate_mask_from_z(z_pos=self.interpolated_z, assign_z=True)
        MaterialMask.remove_overlap_by_priority(*self.optical_structures)
        for structure in self.optical_structures:
            masks[structure.mask.astype(np.bool_)] = structure.n
            structure.clear_old_attr(deep=True)

        return self.interpolated_z, ax_plt[ax1_label], masks

    def clear_preview(self):
        # for structure in self.optical_structures:
        #     structure.clear_old_attr(deep=True)     # remove partially calculated masks from preview
        self._z_list = None
        self._interpolated_z = None

    def preview(self, mode: str = 'xz', position: float = 0., cmap: str = 'Blues', style: str = 'mesh',
                draw_sim_info: bool = True, disp_space_percent: float = 0.03):
        x_annot, y_annot, cut = get_ax_labels(mode=mode)
        x, y, masks = self.get_mask_slice(ax0_label=x_annot, ax1_label=y_annot, cut_label=cut, cut_position=position)
        fig, ax = plt.subplots()
        match style.lower():
            case 'mesh':
                ax.pcolormesh(x, y, masks.astype(float).T, cmap=cmap, shading='auto')
            case 'contour':
                import matplotlib.cm as cm
                cmap = cm.get_cmap(cmap)
                ax.contour(x, y, masks.astype(float).T, [0.999], colors=[cmap(0.999)], linestyles='-')
        if draw_sim_info:
            self.draw_sim_box(ax=ax, x_label=x_annot, y_label=y_annot, offset_percentage=disp_space_percent)
            self.draw_fields(ax=ax, x_label=x_annot, y_label=y_annot)
            self.draw_monitor(ax=ax, x_label=x_annot, y_label=y_annot)

        ax.set_aspect('equal', adjustable='box')
        ax.set_xlabel(x_annot)
        ax.set_ylabel(y_annot)
        ax.set_title(f"{cut} = {position:.2f} um")
        self.clear_preview()

    def draw_monitor(self, ax, x_label, y_label):
        for monitor in self.monitors:
            monitor_axes = list(monitor.type)
            third_ax = {x_label, y_label} - set(monitor_axes)
            if not third_ax:
                continue
            plot_ax = list(third_ax)[0]

            if plot_ax == x_label:
                ax.vlines(monitor.position, ymin=self.sim_lims[y_label][0], ymax=self.sim_lims[y_label][1],
                          colors='tab:green', linestyles='--')
            elif plot_ax == y_label:
                ax.hlines(monitor.position, xmin=self.sim_lims[x_label][0], xmax=self.sim_lims[x_label][1],
                          colors='tab:green', linestyles='--')

    def draw_fields(self, ax, x_label, y_label, show_input=True, show_target=True):
        if self.input_field is not None and show_input:
            # TODO: show all field when combined.
            self.plot_field_loc_angle(field=self.input_field, x_label=x_label, y_label=y_label, ax=ax,
                                      z_pos=self.input_field.z0, color='tab:red', style='-')
        if self.target_field is not None and show_target:
            self.plot_field_loc_angle(field=self.target_field, x_label=x_label, y_label=y_label, ax=ax,
                                      z_pos=self.target_field.z0, color='tab:purple', style='--')

    def annotate_target_beam_radius(self, ax):
        if self.target_field is not None and isinstance(self.target_field.distribution, GaussianDistribution):
            ax.axhline(y=self.target_field.distribution.w0x, color='tab:orange', linestyle='--', alpha=0.6)
            ax.axhline(y=self.target_field.distribution.w0y, color='tab:green', linestyle='--', alpha=0.6)

    def draw_sim_box(self, ax, x_label, y_label, offset_percentage=0.01, linewidth=1.2):
        lim1, lim2 = self.sim_lims[x_label], self.sim_lims[y_label]
        ax.plot([lim1[0], lim1[1], lim1[1], lim1[0], lim1[0]],
                [lim2[0], lim2[0], lim2[1], lim2[1], lim2[0]], color="tab:orange", linestyle='--', linewidth=linewidth)

        bt = {'x': self.boundary_parameters['boundary_thickness'][0],
              'y': self.boundary_parameters['boundary_thickness'][1],
              'z': 0}
        bt1, bt2 = bt[x_label], bt[y_label]
        blim1, blim2 = [lim1[0] + bt1, lim1[1] - bt1], [lim2[0] + bt2, lim2[1] - bt2]
        ax.plot([blim1[0], blim1[1], blim1[1], blim1[0], blim1[0]],
                [blim2[0], blim2[0], blim2[1], blim2[1], blim2[0]], color="tab:gray", linestyle='--', alpha=0.6,
                linewidth=linewidth)

        def disp_space(ax_name):
            return np.diff(self.sim_lims[ax_name])[0] * offset_percentage

        sim_run = {'x': [self.sim_lims['x'][0] - disp_space('x'), self.sim_lims['x'][1] + disp_space('x')],
                   'y': [self.sim_lims['y'][0] - disp_space('y'), self.sim_lims['y'][1] + disp_space('y')],
                   'z': [self.sim_z_start-disp_space('z'), self.sim_z_end + disp_space('z')]}

        ax.set_xlim(sim_run[x_label])
        ax.set_ylim(sim_run[y_label])

    @staticmethod
    def plot_field_loc_angle(field: TransverseField, x_label, y_label, ax, z_pos, color='red', style='-', scale=10):
        from scipy.spatial.transform import Rotation as R
        from matplotlib.patches import Ellipse
        import matplotlib.transforms as transforms
        if not isinstance(field.distribution, GaussianDistribution):
            return

        ids = {'x': 0, 'y': 1, 'z': 2}
        idx, idy = ids[x_label], ids[y_label]

        center = np.array([field.distribution.x0 + field.xy_translation['x'],
                           field.distribution.y0 + field.xy_translation['y'],
                           z_pos])

        angles = {'x': field.rx_deg, 'y': field.ry_deg, 'z': np.rad2deg(field.distribution.theta_z_rad)}

        k_vec = np.array([0, 0, 1]).T * scale  # k vector direction
        wx_vec = np.array([field.distribution.w0x, 0, 0]).T  # x vector
        wy_vec = np.array([0, field.distribution.w0y, 0]).T  # y vector

        r = R.from_euler('ZYX', [angles['z'], angles['y'], angles['x']], degrees=True)
        R = r.as_matrix()
        k_vec = R @ k_vec
        wx_vec = R @ wx_vec
        wy_vec = R @ wy_vec

        xc, yc = center[idx], center[idy]
        ax.plot([xc], [yc], '.', color=color)

        ax.quiver([xc], [yc], [k_vec[idx]], [k_vec[idy]], color=color, width=0.005, alpha=0.6)
        ax.plot([xc, xc + wx_vec[idx]], [yc, yc + wx_vec[idy]], color='tab:orange', linestyle=style, alpha=0.6)
        ax.plot([xc, xc + wy_vec[idx]], [yc, yc + wy_vec[idy]], color='tab:green', linestyle=style, alpha=0.6)

        if x_label == 'x' and y_label == 'y':
            ellipse = Ellipse((0, 0), width=field.distribution.w0x * 2, height=field.distribution.w0y * 2,
                              facecolor='none', edgecolor='#15B01A', linestyle='--')
            transf = transforms.Affine2D().rotate_deg(angles['z']).translate(xc, yc)
            ellipse.set_transform(transf + ax.transData)
            ax.add_patch(ellipse)

        # adjust the z limits to see all arrows
        left, right = ax.get_xlim()
        right_new = right + k_vec[idx] if right + k_vec[idx] > right else right
        left_new = left + k_vec[idx] if left + k_vec[idx] < left else left
        ax.set_xlim([left_new, right_new])

    @classmethod
    def build_scene_from_yaml(cls, yaml_filepath):
        import yaml
        with open(yaml_filepath, 'r') as f:
            description = yaml.safe_load(f)
        # TODO: take care of the parameter mapping
        return cls(**description)

    def run_simulation(self, calculate_coupling=False):
        if not self._meshed_structure:
            self.init_structures()
            self._meshed_structure = True
        self.update_scene()
        self.solver.propagate_field(initial_field=self.input_field.field, z_locs=self.z_list,
                                    cal_slice_power=self.cal_slices_power,
                                    cal_slice_beam_r=self.cal_slices_beam_radius,
                                    callback=self._sim_callbacks)
        if calculate_coupling:
            if self.target_field is None:
                print('Coupling cannot be calculated because the target field is not specified.')
                return
            coupling = TransverseField.force_overlap_integral(self.target_field.field, self.monitors[-1].E,
                                                              mute_warning=True)
            print(f"Coupling: {coupling}")
            return coupling


    def current_opt_info(self):
        fmt_str = (f'Iteration: {self.n_iter}{self.optimization_info()}\n' +
                   'Coupling efficiency: ' + Fore.CYAN + f'{self.current_coupling}\n' + Fore.RESET)
        if self.log_file is not None:
            append_to_file(self.log_file, content=fmt_str)
        return fmt_str

    def optimizer_info(self, method):
        if method in OPT_OPTIONS:
            par_str = ',\t'.join([f"{k}: {v}" for k, v in OPT_OPTIONS[method].items()])
        else:
            par_str = ''
        fmt_str = (f'Optimization method: {method}\nOptimization parameters: {par_str}\n'
                   f'Resolution: {self.get_resolution_info()}\n')
        return fmt_str

    def record_original_structure(self):
        self.original_structure = {}
        for monitor in self.monitors:
            x_label, y_label, cut = get_ax_labels(mode=monitor.type)
            (x_, y_, mask_) = self.get_mask_slice(ax0_label=x_label, ax1_label=y_label, cut_label=cut,
                                                  cut_position=monitor.position)
            self.original_structure[monitor.type] = {'x': x_, 'y': y_, 'mask': mask_}

    def draw_original_structure(self, ax, monitor_type):
        if monitor_type not in self.original_structure.keys():
            return
        ax.contour(self.original_structure[monitor_type]['x'], self.original_structure[monitor_type]['y'],
                   self.original_structure[monitor_type]['mask'].astype(float).T,
                   [1], colors=['gray'], linestyles='--', linewidth=1, alpha=0.8)

    def view_monitors(self, export_data_path=None):
        """Plot E-field and structure contour from each monitors during optimization"""
        for monitor in self.monitors:
            ax = monitor.view(plot_property='E', plot_op='abs', cmap='hot',
                              contour=1 / np.e if monitor.type == 'xy' else None,
                              contour_annot=True, normalize_along_z=False,
                              scale_func=(lambda x: np.exp(-30 * x)) if monitor.type != 'xy' else 'linear')
            if export_data_path is not None:
                monitor.export(path=export_data_path)
            if self.n_iter > 1:
                self.draw_original_structure(ax=ax, monitor_type=monitor.type)
        plt.show()

    def get_resolution_info(self):
        resx, resy = self.target_material_res_xy
        res_str = (f"target material resolution: x = {resx} um, y = {resy} um, "
                   f"z = {self.target_material_res_z} um, "
                   f"background resolution: z = {self.target_background_res_z} um")
        return res_str

    def optimize_structures(self, parameters_to_optimize: dict, method='Nelder-Mead', save_results=True,
                            acceptable_coupling_efficiency=None, set_optimized_par=True, record_process=True,
                            file_stamp=None, saving_dir=None, debug=False):
        if method not in ('Powell', 'Nelder-Mead'):
            print(f'The specified method {method} might have some requirement on the function, '
                  f'which may not be fulfilled.')

        if not self._meshed_structure:
            self.init_structures()
            self._meshed_structure = True

        if file_stamp is None:
            file_stamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')

        saving_dir = '.' if ((saving_dir is None) and (save_results or record_process)) else saving_dir
        if record_process:
            self.log_file = os.path.join(saving_dir, f'Optimization_log_{file_stamp}.txt')
            create_file(self.log_file)
            append_to_file(self.log_file, self.optimizer_info(method=method))

        self.parameters_to_optimize = OrderedDict(parameters_to_optimize)    # TODO: check if the parameters are valid
        self.current_pars = self.get_optimization_parameters()

        init_par = self.current_pars.copy()

        def merit_func(pars):   # TODO: costume merit function, e.g., support user input function
            self.n_iter += 1
            self.current_pars = np.asarray(pars)
            self.update_scene(par_values=self.current_pars, update_structure=True)
            self.solver.propagate_field(initial_field=self.input_field.field, z_locs=self.z_list,
                                        callback=self._sim_callbacks if debug else self._opt_callbacks)
            # Both fields are calculated based on wpm grids
            coupling = TransverseField.force_overlap_integral(self.target_field.field, self.monitors[-1].E,
                                                              mute_warning=True)
            if self.n_iter == 1:    # record original structure
                self.record_original_structure()

            self.current_coupling = coupling
            print(self.current_opt_info())

            if debug:
                self.view_monitors(export_data_path=None)
            return 1 - coupling

        coupling_tol = 1 - acceptable_coupling_efficiency if acceptable_coupling_efficiency is not None else None
        try:
            res = scipy.optimize.minimize(merit_func, x0=init_par, method=method, tol=coupling_tol,
                                          options=OPT_OPTIONS[method] if method in OPT_OPTIONS else None)
            # res = scipy.optimize.basinhopping(merit_func, x0=init_par, minimizer_kwargs={"method": "Nelder-Mead"})

            if_success = res['success']
            self.best_pars = res.get('x', self.current_pars)

            status_color = Fore.RED if not if_success else Fore.LIGHTGREEN_EX
            colored_result = status_color + str(if_success) + Fore.RESET

            result_str = (f"{title_standard}\n" + f"Time: {datetime.now().ctime()}" \
                           + "\nOptimization successful: " \
                           + colored_result \
                           + f"\nResolution: {self.get_resolution_info()}"\
                           + f"\nOptimization method: {method}" \
                           + f"\nOptimal parameters:\n{self.optimization_info(parameters=self.best_pars)}\n" \
                           + f"Coupling efficiency: {1 - res['fun']}")

            print(result_str)
            if self.log_file is not None:
                append_to_file(self.log_file, result_str)

            if save_results:
                result_path = os.path.join(saving_dir, f'Optimization_results_{file_stamp}.txt')
                with open(result_path, 'w') as f:
                    f.write(result_str)
            if set_optimized_par:
                self.set_optimization_parameters(self.best_pars)

        except StopIteration:
            print(Fore.RED + 'Optimization failed.')


def main():
    # TODO: draw 1/e along z slices
    from ripple.field import TransverseField
    from ripple.monitors import FieldMonitor
    from ripple.structures import Lens, SingleSurface
    from ripple.field_distribution import GaussianDistribution

    source_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=3, w0y=1.5),
                                   z0=0, refractive_index=1.53, wavelength=1.55)
    target_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=3, w0y=3),
                                   z0=45, refractive_index=1, wavelength=1.55)
    source_field.rotate_field_xy(theta_x_deg=30)

    monitor_yz = FieldMonitor(monitor_type='yz', position=0, saving_path=None)
    monitor_xz = FieldMonitor(monitor_type='xz', position=None, saving_path=None)

    lens1 = Lens(
                 first_surface_type='ConoidalAsphericSurface',
                 first_surface_parameters={'x0': 0, 'y0': 0, 'z0': 5,
                                           'kappa_x': 1, 'rho_x': 0.05,
                                           'kappa_y': 1.1, 'rho_y': 0.05},
                 # first_surface_type='ConoidalSurface',
                 # first_surface_parameters=dict(x0=0., y0=0., z0=-5., kappa=-0.4, rho=0.05),
                 second_surface_type='PlaneSurface',
                 second_surface_parameters=dict(x0=0., y0=0., z0=15.,
                                                nx=0, ny=0.2, nz=0.8),
                 max_radius=14,
                 refractive_index=1.5, priority=1)
    lens1.translate(0., -10, 0.)

    lens2 = SingleSurface(surface_type='ConoidalAsphericSurface',
                          surface_parameters={'x0': 0., 'y0': 0., 'z0': 25,
                                              'kappa_x': 1, 'rho_x': 0.05,
                                              'kappa_y': 1.1, 'rho_y': 0.05},
                          refractive_index=1.6, material_side=1, priority=2, max_radius=15)
    lens2.rotate(theta_x_deg=30, theta_y_deg=0, theta_z_deg=0, order='zyx', method='intrinsic',
                 pivot_point=np.array([0, 0, 25]))

    dummy_scene = CouplingScene(input_field=source_field, target_field=target_field,
                                background_material_index=1.,
                                optical_structures=[lens1, lens2],
                                sim_size_xy=[30, 30], sim_z_start=None, sim_z_end=None,
                                wavelength_sampling_xy=10, material_wavelength_sampling_z=10,
                                background_wavelength_sampling_z=10,
                                monitors=[monitor_yz, monitor_xz],
                                boundary_condition='PML', boundary_parameters=None)
    # dummy_scene.preview(mode='xz', position=0)
    # dummy_scene.preview(mode='yz', position=0)
    # dummy_scene.preview(mode='surfaces')
    # dummy_scene.preview(mode='mesh', position=0)

    opt_pars = {lens1.name: {'Surface': {'first_surface': ['y0', 'z0', 'kappa_x', 'rho_x', 'kappa_y', 'rho_y'],
                                         'second_surface': ['ny', 'nz']},
                             'Rotation': ['x', 'y'],
                             'Translation': ['x', 'y']},
                lens2.name: {'Surface': {'surface': ['z0', 'kappa_x', 'rho_x', 'kappa_y', 'rho_y']},
                             'Rotation': ['x', 'y'],
                             'Translation': ['x', 'y']}}
    dummy_scene.optimize_structures(parameters_to_optimize=opt_pars, acceptable_coupling_efficiency=0.5)
    # dummy_scene.run_simulation()

    # for monitor in dummy_scene.monitors:
    #     monitor.view(property='abs', scale=None)
    # dummy_scene.target_field.view(title='Target field')
    monitor_yz.view(plot_property='E', plot_op='abs')
    plt.show()


if __name__ == '__main__':
    main()
