import numpy as np
import os
import matplotlib.pyplot as plt
import scipy
import pickle

from ripple.field import Transverse<PERSON>ield
from ripple.field_distribution import GaussianDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens, SingleSurface
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_file
from ripple.functionalities.far_field import plot_far_field_from_monitor
from ripple.utils.export_data import plot_field_grid_far_field
from ripple.field_distribution import ImportedCSTFieldDistribution


save_dir = '/home/<USER>/sneaky/data'

bd_values = scipy.io.loadmat(r'../input/bd_valsWGLenses.mat')['bd_valsWGLenses']
test_b, test_d = bd_values[0]

if not os.path.isdir(save_dir):
    os.makedirs(save_dir)


def has_file_with_prefix(folder_path, prefix):
    """
    Check if any file in the folder starts with the given prefix.

    :param folder_path: Path to the folder
    :param prefix: Prefix string to check for
    :return: True if a file with the prefix exists, False otherwise
    """
    if not os.path.isdir(folder_path):
        print(f"Error: '{folder_path}' is not a valid directory.")
        return False

    for filename in os.listdir(folder_path):
        if filename.startswith(prefix):
            return True
    return False

# Imported source from CST
source_field = ImportedCSTFieldDistribution(cst_export_file_path=r'../input/e-field (f=193.75) [1]_subVolume.txt',
                                              cut_axis='z', rough_cut_pos=20.05, skiprows=2, Nix=None, Niy=None)
source_field = TransverseField(distribution=source_field, z0=0, refractive_index=1.53, wavelength=1.55)

# target_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=12.5, w0y=12.5),
#                                z0=400, refractive_index=1, wavelength=source_field.wavelength)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_beam_radius_per_z=True)


for b_val, d_val in bd_values:
    file_name = f'b={b_val:.2f}_d={d_val:.2f}'
    print(file_name)
    lens = SingleSurface(surface_type='EllipsoidalLensSurface',
                         surface_parameters={'x0': 0, 'y0': 0, 'z0': d_val, 'major_half_axis': b_val,
                                             'n_lens': 1.53, 'n_air': 1., 'focal_length': None, 'direction': 1},
                         refractive_index=1.53, material_side=-1, priority=2, max_radius=None)

    scene = CouplingScene(input_field=source_field, target_field=None,
                          background_material_index=1.,
                          optical_structures=[lens],
                          sim_size_xy=[160, 160], sim_z_end=b_val + d_val + 100,
                          wavelength_sampling_xy=8,
                          material_wavelength_sampling_z=8,
                          background_wavelength_sampling_z=4,
                          monitors=[monitor_xz],
                          boundary_condition='ABC', boundary_parameters=None)

    # print(f"Source field resolution before re-meshing [um]: {source_field.res_x, source_field.res_y}")
    # source_field.view()
    # plt.show()

    run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=False,
            name='optimized', sim_result_dir=None, opt_log_dir=None, show_plot=False)
    z, rx, ry = monitor_xz.z_values, monitor_xz.slices_beam_radius_x, monitor_xz.slice_beam_radius_y

    file_path = os.path.join(save_dir, f'{file_name}_xz_monitor_d4sigma_radius.pkl')
    with open(file_path, 'wb') as f:
        pickle.dump((z, rx, ry), f)

    # axes = monitor_xz.view()
    # plt.savefig(os.path.join(save_dir, f'b={b_val:.2f}_d={d_val:.2f}_xz_monitor.jpg'), dpi=500)
    # plt.show()

    # plot_field_grid_far_field(scene.monitors[-1], folder_path=save_dir, prefix=file_name,
    #                           save_figure=True, save_data=True, show_plot=False)

    # plot_field_grid_far_field(monitor_xy, folder_path=save_dir, prefix=file_name,
    #                           save_figure=True, save_data=True, show_plot=True)

    # z_list = monitor_xz.z_values
    # np.save(f'data/z_{plt_title}', z_list)
    # temp = monitor_xz.slices_beam_radius_x.copy()
    # np.save(f'data/beam_radius_{plt_title}', monitor_xz.slices_beam_radius_x)
    # temp[~(monitor_xz.z_values > (d_val + b_val + 10))] = 1e8
    # waist_id = np.argmin(temp)
    # mfr = monitor_xz.slices_beam_radius_x[waist_id]
    # z_loc = monitor_xz.z_values[waist_id]
    # print(f"d = {d_val}, b = {b_val}: w0 = {mfr} at z = {z_loc}")
    # w0_list.append(mfr)
    # z0_list.append(z_loc)

