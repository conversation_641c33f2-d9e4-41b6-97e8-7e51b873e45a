import numpy as np


class FieldDistribution:
    def __init__(self, phase_rad=None):
        self._distributions = [self]
        if phase_rad is not None:
            assert isinstance(phase_rad, (int, float))
        self.phase_rad = phase_rad

    def __call__(self, x_grid=None, y_grid=None):
        fields = []
        for distr in self._distributions:
            field = distr._cal_field_from_grid(x_grid=x_grid, y_grid=y_grid)
            fields.append(field.astype(np.complex64) * distr.phase_factor if distr.phase_factor is not None else field)
        return np.sum(fields, axis=0)

    def _cal_field_from_grid(self, x_grid, y_grid):
        raise NotImplementedError('Must provide a way to calculate the field from x and y grids.')

    @property
    def phase_factor(self):
        if self.phase_rad is None:
            return None
        phase_factor = np.exp(1j * self.phase_rad, dtype=np.complex64)
        return phase_factor

    def combine_with(self, *distributions):
        for distribution in distributions:
            assert isinstance(distribution, FieldDistribution)
            self._distributions.append(distribution)


class GaussianDistribution(FieldDistribution):
    def __init__(self, x0=0, y0=0, w0x=None, w0y=None, amplitude=1., amplitude_offset=0., theta_z_deg=0, phase_rad=None):
        super().__init__(phase_rad=phase_rad)
        self.x0 = x0
        self.y0 = y0
        self._w0x = w0x     # make sure this corresponds to the field waist
        self._w0y = w0y

        self.amplitude = amplitude
        self.amplitude_offset = amplitude_offset

        self.theta_z_rad = np.deg2rad(theta_z_deg)  # Rotation about z axis

    @property
    def w0x(self):
        return self._w0x

    @property
    def w0y(self):
        return self._w0y

    def _cal_field_from_grid(self, x_grid, y_grid):
        # FIXME: the sigma values are wrt E field and the equations are for intensity?? What's going on?
        """ This is the intensity distribution function.
        General Gaussian expression: https://en.wikipedia.org/wiki/Gaussian_function
        Beam waist: https://en.wikipedia.org/wiki/Gaussian_beam#Beam_waist"""
        sigma_x = self.w0x * 1 / np.sqrt(2)     # w**2 = 2 * sigma**2
        sigma_y = self.w0y * 1 / np.sqrt(2)
        a = (np.cos(self.theta_z_rad) ** 2) / (2 * sigma_x ** 2) + (np.sin(self.theta_z_rad) ** 2) / (2 * sigma_y ** 2)
        b = (np.sin(2 * self.theta_z_rad)) / (4 * sigma_x ** 2) - (np.sin(2 * self.theta_z_rad)) / (4 * sigma_y ** 2)
        c = (np.sin(self.theta_z_rad) ** 2) / (2 * sigma_x ** 2) + (np.cos(self.theta_z_rad) ** 2) / (2 * sigma_y ** 2)
        # General expression of a 2D elliptical Gaussian function
        g = self.amplitude_offset + self.amplitude * np.exp(- (a * ((x_grid - self.x0) ** 2)
                                           + 2 * b * (x_grid - self.x0) * (y_grid - self.y0)
                                           + c * ((y_grid - self.y0) ** 2)))
        return g


class UniformDistribution(FieldDistribution):
    def __init__(self, amplitude=1., phase_rad=None):
        super().__init__(phase_rad=phase_rad)
        self.amplitude = amplitude


    def _cal_field_from_grid(self, x_grid, y_grid):
        assert x_grid.shape == y_grid.shape
        return self.amplitude * np.ones(shape=x_grid.shape)


class ImportedFieldDistribution(FieldDistribution):
    def __init__(self, file_path=None, phase_rad=None, *args, **kwargs):
        super().__init__(phase_rad=phase_rad)
        if file_path is not None:
            from ripple.utils.interpolations import interpolate_field
            x_grid, y_grid, field = \
                self.import_field_from_file(file_path=file_path, *args, **kwargs)
            self.interpolated_distribution = interpolate_field(x_grid, y_grid, field)
        else:
            self.interpolated_distribution = None

    def _cal_field_from_grid(self, x_grid, y_grid):
        assert self.interpolated_distribution is not None
        return self.interpolated_distribution(x_grid, y_grid)

    def import_field_from_file(self, file_path, *args, **kwargs):
        # return x_grid, y_grid, field
        raise NotImplementedError('Not implemented yet.')

    @staticmethod
    def linearly_interpolate(x, y, z, Nix, Niy):
        """x, y and z has the shape of 1d array."""
        import scipy.interpolate as interpolate
        xi = np.linspace(x.min(), x.max(), num=Nix)
        yi = np.linspace(y.min(), y.max(), num=Niy)
        xxi, yyi = np.meshgrid(xi, yi)
        interp_linear = interpolate.LinearNDInterpolator(np.vstack([x, y]).T, z, fill_value=0.)
        E_interp = interp_linear(np.vstack([xxi.flatten(), yyi.flatten()]).T)
        E_interp = E_interp.reshape(xxi.shape)
        return xxi.T, yyi.T, E_interp.T


class ImportedCSTFieldDistribution(ImportedFieldDistribution):
    def __init__(self, cst_export_file_path=None, cut_axis='z', rough_cut_pos=10, skiprows=2,
                 Nix=None, Niy=None, phase_rad=None):
        super().__init__(file_path=cst_export_file_path, cut_axis=cut_axis, rough_cut_pos=rough_cut_pos, skiprows=skiprows,
                 Nix=Nix, Niy=Niy, phase_rad=phase_rad)

    def import_field_from_file(self, file_path, cut_axis='z', rough_cut_pos=0., skiprows=2, grid_unit='um',
                                Nix=None, Niy=None):
        """Assunme that light propagate along z direction.
        Only support linear interpolation because spline interpolation cause negative |E| value, which is wrong.
        Nix: number of interpolated points along x direction
        Niy: number of interpolated points along y direction
        return: x_grid [um], y_grid [um], E_grid
        """
        assert file_path.endswith('.txt'), 'Only support CST exported txt file.'

        # convert grid units to um
        unit_factor = {'m': 1e6, 'mm': 1e3, 'um': 1}[grid_unit]
        import pandas as pd

        x_label = 'x' if cut_axis == 'z' else 'z'
        y_label = 'xyz'.replace(cut_axis, '').replace(x_label, '')

        column_names = ['x [m]', 'y [m]', 'z [m]', 'ExRe [V/m]', 'ExIm [V/m]',
                        'EyRe [V/m]', 'EyIm [V/m]', 'EzRe [V/m]', 'EzIm [V/m]']
        data = pd.read_csv(file_path, sep='\s+', skiprows=skiprows, names=column_names)
        # data = pd.read_csv(file_path, sep='\s+', names=column_names)

        ax_unique = np.asarray(data[f'{cut_axis} [m]'].unique()) * unit_factor
        target_value = ax_unique[np.argmin(np.abs(ax_unique - rough_cut_pos))]
        slice_data = data[data[f'{cut_axis} [m]'] == target_value / unit_factor]

        x = slice_data[f'{x_label} [m]'].to_numpy() * unit_factor
        y = slice_data[f'{y_label} [m]'].to_numpy() * unit_factor
        ExRe = slice_data['ExRe [V/m]'].to_numpy()
        ExIm = slice_data['ExIm [V/m]'].to_numpy()
        EyRe = slice_data['EyRe [V/m]'].to_numpy()
        EyIm = slice_data['EyIm [V/m]'].to_numpy()
        EzRe = slice_data['EzRe [V/m]'].to_numpy()
        EzIm = slice_data['EzIm [V/m]'].to_numpy()

        Ex = ExRe + 1j * ExIm
        Ey = EyRe + 1j * EyIm
        Ez = EzRe + 1j * EzIm
        E_abs = np.sqrt(np.abs(Ex) ** 2 + np.abs(Ey) ** 2 + np.abs(Ez) ** 2)
        # E = np.vstack([Ex, Ey, Ez]).T

        x_vals = np.unique(x)
        y_vals = np.unique(y)
        Nx = len(x_vals)
        Ny = len(y_vals)
        assert Nx * Ny == len(E_abs)
        if not Nix and not Niy:
            x_grid = x.reshape(Ny, Nx)
            y_grid = -y.reshape(Ny, Nx)  # y in CST points downwards, only inverse the y-axis, the field is correct.
            E_grid = E_abs.reshape(Ny, Nx)
            return x_grid.T, y_grid.T, E_grid.T  # Follow wpm convention, x--row, y--column
        Nix = Nix if Nix else Nx
        Niy = Niy if Niy else Ny
        return self.linearly_interpolate(x=x, y=-y, z=E_abs, Nix=Nix, Niy=Niy)


class ImportedImageFieldDistribution(ImportedFieldDistribution):
    def __init__(self, image_file_path=None, phase_rad=None, *args, **kwargs):
        super().__init__(file_path=image_file_path, phase_rad=phase_rad, *args, **kwargs)

    def import_field_from_file(self, file_path, pixel_size_um=0.15):
        """
        Load a TIFF image file and process it to extract electric field distribution data.

        Parameters:
        -----------
        file_path : str
            Path to the TIFF image file (must have .tiff extension)
        pixel_size_um : float, optional
            Physical size per pixel in micrometers (default: 0.5)

        Returns:
        --------
        tuple
            (x_grid, y_grid, E_field) where:
            - x_grid, y_grid: 2D coordinate grids centered at (0,0)
            - E_field: normalized electric field magnitude (max value = 1.0)
        """
        assert file_path.endswith(('.png', '.tiff')), 'Only support image file with type .png or .tiff.'

        try:
            from PIL import Image
            import os

            # Check if file exists
            if not os.path.isfile(file_path):
                raise FileNotFoundError(f"Image file not found: {file_path}")

            # Load the TIFF image
            with Image.open(file_path) as img:
                image_data = np.array(img, dtype=np.float64)

                # plt.matshow(image_data, cmap='gray')
                # plt.show()

            height, width = image_data.shape

            # Create coordinate grids centered at (0,0)
            # x corresponds to columns (width), y corresponds to rows (height)
            x_extent = width * pixel_size_um
            y_extent = height * pixel_size_um

            # Create 1D coordinate arrays centered at origin
            x_1d = np.linspace(-x_extent/2, x_extent/2, width)
            y_1d = np.linspace(-y_extent/2, y_extent/2, height)
            x_grid, y_grid = np.meshgrid(x_1d, y_1d)

            # Normalize the electric field so maximum value equals 1.0
            E_field = image_data.astype(np.float64)
            max_value = np.max(E_field)
            if max_value > 0:
                E_field = E_field / max_value
            else:
                # Handle case where image is all zeros
                E_field = np.zeros_like(E_field)
            
            # Follow the convention used in other methods: x--row, y--column
            # Transpose to match the expected output format
            return x_grid, y_grid[::-1], E_field

        except ImportError:
            raise ImportError("PIL (Python Imaging Library) is required for image loading. Install with: pip install Pillow")
        except Exception as e:
            raise RuntimeError(f"Error loading TIFF image from {file_path}: {str(e)}")


if __name__ == '__main__':
    import matplotlib.pyplot as plt
    # field = ImportedCSTFieldDistribution()
    # x_grid, y_grid, E_grid = field.import_field_from_file(
    #     file_path=r'C:\Users\<USER>\Documents\PycharmProject\ripple\scripts\OPALID_Cyl\input\e-field (f=193.75) [1]_subVolume.txt',
    #     cut_axis='z', rough_cut_pos=20.05, skiprows=2, Nix=None, Niy=None)
    # fig, ax = plt.subplots(1, 1, figsize=(5, 4), sharex=True, sharey=True)
    # im = ax.pcolormesh(x_grid, y_grid, E_grid, cmap='viridis', shading='nearest')
    # plt.colorbar(im)
    # plt.tight_layout()
    # plt.show()

    field = ImportedImageFieldDistribution()
    x_grid, y_grid, E_grid = field.import_field_from_file(
        file_path=r'D:\WORK\02_Research\10_EDWA_packaging\08_Samples\Dummy\0_0.0.tiff', pixel_size_um=0.15)
    fig, ax = plt.subplots(1, 1, figsize=(5, 4), sharex=True, sharey=True)
    im = ax.pcolormesh(x_grid, y_grid, E_grid, cmap='viridis', shading='nearest')
    plt.colorbar(im)
    plt.tight_layout()
    plt.show()
    
