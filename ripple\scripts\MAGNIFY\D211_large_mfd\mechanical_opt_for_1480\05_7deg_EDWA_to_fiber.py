import numpy as np
import os
import matplotlib.pyplot as plt

from ripple.field import Trans<PERSON><PERSON><PERSON>
from ripple.field_distribution import GaussianDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens, SingleSurface
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.geometry import rotate_vector
from ripple.utils.optics import cal_exit_angle
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_dir


coupling_distance = 300 + 550
coupling_angle_deg = -7
c_wl = 1.48
global_x_offset = 30

monitor_xy = FieldMonitor(monitor_type='xy', position=0.1, saving_path=None)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_power_per_z=True,
                          record_beam_radius_per_z=True)

# Coupling efficiency: 0.9683862493026612
lens1 = SingleSurface(
            surface_type='ConoidalAsphericSurface',
            surface_parameters={'x0': -7.978863859804147, 'y0': 0, 'z0': 110.16152924026639,
                                'rho_x': -0.02737759037676745, 'kappa_x': -0.6268512943973049,
                                'rho_y': -0.027365917952818994, 'kappa_y': -0.4758349300796928},
            max_radius=125/2, material_side=-1, refractive_index=1.53, priority=2)
lens1.translate(tx=global_x_offset, ty=0, tz=0)

lens2_len = 260
lens2 = SingleSurface(
            surface_type='ConoidalAsphericSurface',
            surface_parameters={'x0': 20.70687818603306, 'y0': 0, 'z0': coupling_distance - lens2_len,
                                'rho_x': 0.010686954237871762, 'kappa_x': -0.5636363742616953,
                                'rho_y': 0.008949203415374404, 'kappa_y': -0.5070492013051033},
            max_radius=125/2, material_side=1, refractive_index=1.53, priority=2)
lens2_x_offset = ((coupling_distance - lens1._surface_parameters['z0'] - lens2_len)
                  * np.sin(np.deg2rad(coupling_angle_deg))) +  global_x_offset

source_field = TransverseField(distribution=GaussianDistribution(x0=global_x_offset, y0=0, w0x=1.67, w0y=1.3),  # FIXME: is the mode field right here?
                               z0=0, refractive_index=1.53, wavelength=c_wl)
target_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=10.4 / 2, w0y=10.4 / 2),
                               z0=coupling_distance, refractive_index=1.53, wavelength=c_wl)
# tx is from the optimized value
lens2.translate(tx=-29.415095227086308, ty=0, tz=0, attach_to=target_field, attach_after_translation=False)

scene = CouplingScene(input_field=source_field, target_field=target_field,
                      background_material_index=1.,
                      optical_structures=[lens1, lens2],
                      sim_size_xy=[250, 250],
                      # sim_z_end=target_center_z + 10,
                      wavelength_sampling_xy=4,
                      material_wavelength_sampling_z=4,
                      background_wavelength_sampling_z=2,
                      solver='wpm',
                      # solver='bvwpm', solver_pars={'polarization': 'x'},
                      monitors=[monitor_xy, monitor_xz],
                      boundary_condition='PML', boundary_parameters={"boundary_thickness": [5]*2})

if __name__ == "__main__":
    # current_dir = os.path.abspath(os.path.dirname(__file__))
    # save_dir = os.path.join(current_dir, 'gaussian_fit')
    save_dir=None
    if False:
        scene.preview(mode='xz', position=0)
        scene.preview(mode='yz', position=0)
        plt.show()

    if True:
        opt_pars = {
            lens1.name: {
                'Surface': {
                    'surface': ['x0', 'z0', 'kappa_x', 'kappa_y', 'rho_x', 'rho_y'],
                },
                # 'Translation': ['x']
            },
            lens2.name: {
                'Surface': {
                    'surface': ['x0', 'kappa_x', 'kappa_y', 'rho_x', 'rho_y'],
                },
                # 'Translation': ['x']
            }
        }

        run_sim(scene, optimize_structure=True, sim_result_dir=save_dir, opt_log_dir=save_dir,
                opt_pars=opt_pars, cal_coupling=True, show_plot=True)

    if False:
        plot_monitor_from_dir(dir_path=r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\02_Fiber_1chEDWA4_01_Fiber', scene=scene)
        plt.show()

    if False:
        monitor_path = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\03_Lens_design\04_QDMLLD_J5_to_SiN_B4_10cm\Design3_20deg\QDMLLD_SiN__lenses_d4\1_monitor_xy_2024-10-24_14-08-57.pkl'
        monitor = FieldMonitor.load(path=monitor_path)

        ce = monitor.cal_overlap_integral(input_field=source_field, field_type='E', operation='abs', direction='reflected', debug=True)
        print(f"{ce = }")   # 0.0028328535 (4, 4, 2)

