from measurement.MFD_measurement.mfd_measurer import MFDMeas
import os.path

# Pump current was set to 600 mA
SAVE_DIR = r'D:\WORK\02_Research\10_EDWA_packaging\08_Samples\12_D213\MFD\C9_doped_3spirals_500nm_taper_1480nm_OD1_left_side'

mfd_meas = MFDMeas(stage_COM='COM4', initial_exposure=None, pixel_size_um=0.15, wavelength_um=1.55,
                   image_suffix='.tiff', camera_verbose=True,
                   max_saturated_pixels=1, target_pixel_brightness_percent=0.99,
                   mask_diameter=5, phi=0)
# mfd_meas.initialize_stage()
# mfd_meas.generate_equidistance_delta_z(z_step=-0.1, nr_pos=10)

# mfd_meas.calibrate_beam_direction(max_range=-0.06)
# mfd_meas.meas_mfd(save_dir=SAVE_DIR, show_each_plot=False, return_to_original_pos=True)
# mfd_meas.fit_mfd(image_dir=SAVE_DIR, result_dir=SAVE_DIR, plot_each_fit=False, print_fit_info=False,
#                  fit_simple_gaussian=True)
# mfd_meas.fit_mfd(image_dir=SAVE_DIR, result_dir=SAVE_DIR, plot_each_fit=False, print_fit_info=False,
#                  fit_simple_gaussian=False)
mfd_meas.fit_single_image(os.path.join(SAVE_DIR, '0_0.0.tiff'), save_plot=True)


