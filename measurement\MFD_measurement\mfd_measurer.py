import os
import sys
import numpy as np
from functools import partial
import matplotlib.pyplot as plt
import traceback
import imageio.v3 as iio

import laserbeamsize as lbs
from vmbpy import PixelFormat
from device_wrappers.SMC100.smc100 import smc100
from device_wrappers.Goldeye.goldeye_G034_TEC1 import *

# TODO:
#  1. Check if the pixel size and the wavelength is correct.
#  2. Do not use SMC100 utility in between or there might be some error with the stage control.
#  3. During the measurement, do not touch the ethernet cable connected to the camera to prevent vibration in the image!


class MFDMeas:
    def __init__(self, stage_COM='COM4', initial_exposure=None, pixel_size_um=0.2525, wavelength_um=1.55,
                 image_suffix='.tiff', camera_verbose=False, max_saturated_pixels=3,
                 target_pixel_brightness_percent=0.99, mask_diameter=3, phi=0):
        self.stage_COM = stage_COM
        self.exposure_time = initial_exposure

        self._delta_z = None
        self.original_pos = None
        self.original_exposure = 1

        self.pixel_size_um = pixel_size_um  # G-034 has pixel size of 15 um
        self.wavelength_um = wavelength_um

        if image_suffix[0] != '.':
            self.image_suffix = '.' + image_suffix
        else:
            self.image_suffix = image_suffix
        #  The rectangular mask is `mask_diameters` times the pixel diameters of the ellipse.
        self.mask_diameter = mask_diameter
        self.phi = phi

        self.adjust_exposure_func = partial(adjust_exposure,
                                            initial_exposure=None,
                                            target_brightness_percent=target_pixel_brightness_percent,
                                            max_saturated_pixels=max_saturated_pixels, debug_mode=camera_verbose)

    @property
    def delta_z(self):
        return self._delta_z

    @delta_z.setter
    def delta_z(self, value):
        self._delta_z = value

    def initialize_stage(self):
        print("Initializing stage will move it to z=0, are you sure it will not crush the sample? ", end='')
        self.if_proceed()
        with smc100(COM=self.stage_COM) as smc:
            smc.reset()
            smc.home()

    def estimate_mfd(self):
        with (
            VmbSystem.get_instance(),
            get_camera() as cam,
        ):
            if self.exposure_time is not None:
                set_exposure(cam, self.exposure_time)
            self.exposure_time = self.adjust_exposure_func(cam)

    def generate_equidistance_delta_z(self, z_step, nr_pos):
        self.delta_z = z_step * np.ones(nr_pos)
        print(f"Relative Z steps: {self.delta_z}")

    def if_proceed(self):
        if_continue = input("Continue? [y/n] ")
        if if_continue != 'y':
            sys.exit()

    def cal_z_positions_from_Rayleigh_range(self, z_r, nr_points: int):
        half_points = np.round(nr_points/2, 0).astype(int)
        z_within = np.linspace(0, z_r * 1.5, half_points)
        z_beyond = np.linspace(1.5 * z_r, 5 * z_r, nr_points - half_points)
        return np.concatenate([z_within, z_beyond])

    def cal_delta_z_from_current_mfd(self, camera, nr_sample_each_direction=30):
        """ISO 11146-1 standard requires:
        ... at least 10 different z positions shall be taken. Approximately half of the measurements
        shall be distributed within one Rayleigh length on either side of the beam waist, and approximately half of
        them shall be distributed beyond two Rayleigh lengths from the beam waist."""
        self.exposure_time = self.adjust_exposure_func(camera)
        init_frame_np = self.get_image_np(camera)
        print(f'Analyzing the first frame...')
        x, y, dx, dy, phi = lbs.beam_size(init_frame_np, mask_diameters=self.mask_diameter, phi=self.phi, iso_noise=False)
        mfd_x = dx * self.pixel_size_um
        mfd_y = dy * self.pixel_size_um
        print(f" - The center of the beam ellipse is at ({x:.0f}, {y:.0f})")
        print(f' - MFD_x = {mfd_x:.2f} um, MFD_y = {mfd_y:.2f} um')
        lbs.plot_image_analysis(init_frame_np, mask_diameters=self.mask_diameter, pixel_size=self.pixel_size_um, units='µm',
                                crop=True, iso_noise=False, phi=self.phi)
        plt.show()

        z_r_x = self.cal_Rayleigh_range(w0=mfd_x/2)
        z_r_y = self.cal_Rayleigh_range(w0=mfd_y/2)
        print(f' - Rayleigh range z_x = {z_r_x:.2f} um, z_y = {z_r_y:.2f} um')
        z_pos_x = self.cal_z_positions_from_Rayleigh_range(z_r=z_r_x, nr_points=nr_sample_each_direction)
        z_pos_y = self.cal_z_positions_from_Rayleigh_range(z_r=z_r_y, nr_points=nr_sample_each_direction)
        z_pos = np.sort(np.unique(np.concatenate([z_pos_x, z_pos_y])))
        self.delta_z = -np.diff(z_pos) * 1e-3   # convert from um to mm
        print(f"Total number of z locations: {len(self.delta_z)}")
        self.if_proceed()

    def save_frame(self, img_name, save_dir, frame):
        img_path = img_name if save_dir is None else os.path.join(save_dir, img_name)
        # Only mono8 or mono16 is compatible with openCV
        cv2.imwrite(img_path, frame.convert_pixel_format(PixelFormat.Mono16).as_opencv_image())
        # cv2.imwrite(img_path, frame.convert_pixel_format(PixelFormat.Mono8).as_opencv_image())

    def cal_Rayleigh_range(self, w0, M2=1):
        """Input and output are all in um."""
        return np.pi * w0**2 / self.wavelength_um / M2

    @staticmethod
    def get_image_np(camera):
        frame = camera.get_frame()
        return frame.as_numpy_ndarray().squeeze()

    def get_spot_center(self, camera, show_analysis=True):
        self.exposure_time = self.adjust_exposure_func(camera)
        image_np = self.get_image_np(camera)
        x, y, dx, dy, phi = lbs.beam_size(image_np, mask_diameters=self.mask_diameter, phi=self.phi,
                                          iso_noise=False)
        print(f'Center pixel {x = }, {y = }')
        if show_analysis:
            lbs.plot_image_analysis(image_np, mask_diameters=self.mask_diameter, pixel_size=self.pixel_size_um,
                                    units='µm', crop=True, iso_noise=False, phi=self.phi)
            plt.show()
        return x, y

    def calibrate_beam_direction(self, max_range=-0.01):
        assert max_range < 0, 'The camera should move away (negative value)'
        with (
            VmbSystem.get_instance(),
            get_camera() as cam,
            smc100(COM=self.stage_COM) as stage
        ):
            cam.set_pixel_format(PixelFormat.Mono14)
            original_exposure = cam.ExposureTime.get()
            print("z at 0")
            x0, y0 = self.get_spot_center(cam, show_analysis=True)
            stage.move_relative(max_range)  # Move away

            print(f"z at {max_range}")
            x1, y1 = self.get_spot_center(cam, show_analysis=True)
            set_exposure(cam, original_exposure * 0.9)
            stage.move_relative(-max_range)  # Move back

            delta_x = (x1 - x0) * self.pixel_size_um
            delta_y = (y1 - y0) * self.pixel_size_um
            x_deg = np.rad2deg(np.arctan2(delta_x, -max_range * 1e3))
            y_deg = np.rad2deg(np.arctan2(delta_y, -max_range * 1e3))
            print(f"x_diff: {x1 - x0} pixels\t{delta_x} um\t{x_deg} deg")
            print(f"y_diff: {y1 - y0} pixels\t{delta_y} um\t{y_deg} deg")
            cam.set_pixel_format(PixelFormat.Mono8)

    @staticmethod
    def check_dir(dir_path):
        if not os.path.exists(dir_path):
            if_create = input('Directory does not exist. Create it? (y/n): ')
            if if_create == 'y':
                os.makedirs(dir_path)
            else:
                sys.exit('Exiting...')

    def meas_mfd(self, save_dir=None, show_each_plot=False, return_to_original_pos=False):
        last_check = input('Did you turn off the illumination? (y/n): ')
        if last_check == 'y':
            pass
        else:
            sys.exit('Exiting...')
        with (
            VmbSystem.get_instance(),
            get_camera() as cam,
            smc100(COM=self.stage_COM) as stage
        ):
            self.check_dir(save_dir)
            cam.set_pixel_format(PixelFormat.Mono14)
            if self.delta_z is None:
                self.cal_delta_z_from_current_mfd(camera=cam)
            if not np.all(self.delta_z < 0):
                print(f'The camera will move towards the chip. ', end='')
                self.if_proceed()
            current_position = stage.get_position()
            if isinstance(current_position, float):
                final_pos = current_position + np.sum(self.delta_z)
                assert 0 <= final_pos <= 10, (f"Final position {final_pos} will be out of range. "
                                              f"Current position {stage.get_position()}.")

            original_exposure = cam.ExposureTime.get()
            self.original_pos = abs(stage.get_position())

            # save the first frame
            img_name = f'0_0.0' + self.image_suffix
            self.save_frame(img_name=img_name, save_dir=save_dir, frame=cam.get_frame())

            abs_pos = np.cumsum(self.delta_z)

            try:
                for i, delta_z in enumerate(self.delta_z):
                    stage.move_relative(delta_z)

                    self.exposure_time = self.adjust_exposure_func(cam)
                    _, _, frame, frame_np = analyze_frame(cam)

                    img_name = f'{i + 1}_{abs_pos[i]}' + self.image_suffix
                    self.save_frame(img_name=img_name, save_dir=save_dir, frame=frame)

                    if show_each_plot:
                        plt.imshow(frame_np, cmap='gray')
                        plt.title(img_name)
                        plt.show()
            except Exception:
                print(f"Measurement failed")
                print(traceback.format_exc())
                set_exposure(cam, original_exposure * 0.7)
                stage.move_absolute(self.original_pos)
                raise 'Measurement failed.'

            if return_to_original_pos:
                set_exposure(cam, original_exposure * 0.7)
                stage.move_absolute(self.original_pos)
                print("Returned to original position")
            cam.set_pixel_format(PixelFormat.Mono8)

    def fit_mfd(self, image_dir, result_dir, plot_each_fit=False, print_fit_info=True,
                fit_simple_gaussian=False, crop_image=False):
        from process_measurement.MFD_setup.mfd_fit.mfd_fit import MFDFitter
        if fit_simple_gaussian:
            print("If the mode field is multi-mode, please set fit_simple_gaussian to False!")
        self.check_dir(image_dir)
        self.check_dir(result_dir)
        mfd_fitter = MFDFitter(image_dir=image_dir,
                               image_suffix=self.image_suffix, wavelength_m=self.wavelength_um * 1e-6,
                               mask_diameter=self.mask_diameter, phi=self.phi,
                               pixel_size_um=self.pixel_size_um, fit_gaussian=fit_simple_gaussian, crop_img=crop_image)
        mfd_fitter.fit_mfd(save_figures=result_dir, plot_each_fit=plot_each_fit, print_fit_info=print_fit_info)

    def fit_single_image(self, image_path, save_plot=False):
        img = iio.imread(image_path)
        x, y, dx, dy, phi = lbs.beam_size(img, mask_diameters=self.mask_diameter, phi=self.phi,
                                          iso_noise=False)
        mfd_x = dx * self.pixel_size_um
        mfd_y = dy * self.pixel_size_um
        print(f" - The center of the beam ellipse is at ({x:.0f}, {y:.0f})")
        print(f' - MFD_x = {mfd_x:.2f} um, MFD_y = {mfd_y:.2f} um')
        lbs.plot_image_analysis(img, mask_diameters=self.mask_diameter, pixel_size=self.pixel_size_um,
                                units='µm', crop=True, iso_noise=False, phi=self.phi)
        image_name = '.'.join(image_path.split('.')[:-1])
        result_path = os.path.join(os.path.dirname(image_path), image_name + '_mfd_fit.png')
        if save_plot:
            print(f'Save result to {result_path}')
            plt.savefig(result_path, dpi=300)
        plt.show()
