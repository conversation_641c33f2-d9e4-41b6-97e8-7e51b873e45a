import sys
import traceback

import cv2
import numpy as np
import vmbpy
import matplotlib.pyplot as plt
from vmbpy import VmbSystem
from vmbpy import PixelFormat
import time

# G034 pixel size: 15 um

PIXEL_LIM = {
    PixelFormat.Mono8: 2**8-1,
    PixelFormat.Mono12: 2**12-1,
    PixelFormat.Mono14: 2**14-1
}


def abort(reason: str, return_code: int = 1):
    print(reason + '\n')
    sys.exit(return_code)


def get_camera(camera_id=None) -> vmbpy.Camera:
    with VmbSystem.get_instance() as vmb:
        if camera_id:
            try:
                return vmb.get_camera_by_id(camera_id)

            except vmbpy.VmbCameraError:
                abort('Failed to access Camera \'{}\'. Abort.'.format(camera_id))

        else:
            cams = vmb.get_all_cameras()
            if not cams:
                abort('No Cameras accessible. Abort.')

            return cams[0]


def set_exposure(camera, exposure: float):
    if isinstance(exposure, str) and exposure.lower().strip() == 'auto':
        camera.ExposureAuto.set('Continuous')
    else:
        camera.ExposureTime.set(float(exposure))


def analyze_frame(camera, max_pixel_val=None):
    """ Capture frame and analyze its pixel intensity distribution """
    frame = camera.get_frame()
    frame_np = frame.as_numpy_ndarray()

    # Get the maximum brightness
    max_brightness = np.amax(frame_np)

    # Calculate the number of saturated pixels
    num_saturated_pixels = np.sum(frame_np >= max_pixel_val) if max_pixel_val else None

    return max_brightness, num_saturated_pixels, frame, frame_np.squeeze()


def adjust_exposure(camera, initial_exposure=1000., target_brightness_percent=0.98,
                    max_saturated_pixels=10, max_trials=300,
                    Kp=0.1, Ki=0.005, Kd=0.01, saturated_weight=10, debug_mode=False):
    """
    Adjust the exposure using a PID control approach, taking into account both the brightness and
    the number of saturated pixels to avoid overexposure.
    """
    print(f"Adjusting exposure...", end='')
    exposure = initial_exposure
    if exposure:
        set_exposure(camera, exposure)
    else:
        exposure = camera.ExposureTime.get()
    safe_exposure = exposure

    previous_error = 0
    integral = 0
    last_time = time.time()

    pixel_max = PIXEL_LIM[camera.get_pixel_format()]
    target_brightness = target_brightness_percent * pixel_max
    tolerance = (1 - target_brightness_percent) * pixel_max

    max_brightness, num_saturated_pixels = None, None
    success = False
    try:
        for i in range(max_trials):
            max_brightness, num_saturated_pixels, _, _ = analyze_frame(camera, max_pixel_val=pixel_max)
            if debug_mode:
                print(f'Iteration {i}: Exposure = {exposure}, Max Brightness = {max_brightness}, '
                      f'Saturated Pixels = {num_saturated_pixels}')

            brightness_error = max_brightness - target_brightness
            saturation_diff = num_saturated_pixels - max_saturated_pixels
            if 0 <= brightness_error <= tolerance and num_saturated_pixels <= max_saturated_pixels:
                success = True
                break

            if saturation_diff < 0:
                saturation_error = 0
            else:
                saturation_error = saturation_diff

            combined_error = brightness_error + saturated_weight * saturation_error

            # Proportional term (based on the current combined error)
            P = Kp * combined_error

            # Integral term (accumulating past errors)
            current_time = time.time()
            delta_time = min(current_time - last_time, 0.1)
            integral += combined_error * delta_time
            # integral += combined_error
            I = Ki * integral

            # Derivative term (rate of error change)
            derivative = (combined_error - previous_error) / delta_time
            # derivative = combined_error - previous_error
            D = Kd * derivative

            # Calculate the total adjustment
            adjustment = P + I + D
            if np.isclose(adjustment, 0):
                success = True
                break
            adjustment_factor = 1 - adjustment / target_brightness

            # Update exposure based on adjustment factor
            exposure *= adjustment_factor
            # print(f"{P = }, {I = }, {D = }, {adjustment_factor = }")

            # Clamp the exposure to reasonable values
            exposure = max(1, min(exposure, 1e6))

            # Apply the new exposure
            set_exposure(camera, exposure)
            # time.sleep(0.2)  # the camera needs a bit time

            # Update previous error and time for the next iteration
            previous_error = combined_error
            last_time = current_time
    except Exception as e:
        set_exposure(camera, safe_exposure)
        traceback.format_exception(e)
        raise 'Failed to adjust the exposure. Abort.'
    if not success:
        set_exposure(camera, safe_exposure)
        raise 'Failed to adjust the exposure. Abort.'
    print(f'done. Max Brightness = {max_brightness} ({max_brightness/pixel_max * 100:.2f}%), '
          f'Saturated Pixels = {num_saturated_pixels}')
    return exposure


def main():
    with VmbSystem.get_instance():  # do we need VmbSystem.get_instance?
        with get_camera() as cam:
            # temp = str(cam.get_pixel_format())
            # print(f"{cam.get_pixel_formats()}")
            # Adjust exposure to avoid saturation while achieving target brightness
            optimal_exposure = adjust_exposure(
                cam, initial_exposure=20000., target_brightness_percent=0.98,
                max_saturated_pixels=3, debug_mode=True
            )

            # Final frame after adjustment
            _, _, frame, frame_np = analyze_frame(cam, max_pixel_val=None)
            # print(f"{frame.get_pixel_format()}")
            # print(f"{frame.get_pixel_formats()}")
            plt.imshow(frame_np, cmap='gray')
            plt.show()
            cv2.imwrite('frame.png', frame.convert_pixel_format(PixelFormat.Mono8).as_opencv_image())
            print(f'Final exposure time: {optimal_exposure}')


if __name__ == '__main__':
    main()
