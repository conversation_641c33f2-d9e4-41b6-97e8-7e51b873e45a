import os.path
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
import glob
from scipy.constants import c
from utils import estimate_intrinsic_linewidth, plot_multiple_frequency_noise, cal_freq_noise_from_IQ, plot_single_frequency_noise

from utils import read_csv, get_average_values_from_dir
from process_measurement.utils.formatting import define_scale, format_large_number
plt.rcParams['font.family'] = 'arial'
plt.rcParams['font.size'] = 8
plt.rcParams['savefig.dpi'] = 300


def Linewidth_compare_CLEO_2025():
    frequencies = []
    noises = []

    # Load free running data
    folder_path = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\07_Optical_linewidth\Free_running\2024_11_26_free_running_IQ\rec7'
    file_name = 'IQ_free_running_QDMLLD_49p7GHz_LaserCurrent_225mA_span80M_RBW23k_wo_amplifier_rec8.csv'
    x_values, y_values = read_csv(os.path.join(folder_path, file_name), start_line=32, nr_points=100000, delimiter='\t')
    frequency_fr, noise_fr = cal_freq_noise_from_IQ(x_values, y_values, sampling_rate=37502094.028235, tau=1 / 13.536e6,
                                                    method='mlab_psd')
    frequencies.append(frequency_fr)
    noises.append(noise_fr)
    # np.save('offset_frequency', frequency_fr)
    # np.save('frequency_noise', noise_fr)

    # Locked data
    locked_paths = [
        r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\07_Optical_linewidth\Locked\1527p14\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p7mA_newfocus1527p14nm_rec4.csv',
        r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\07_Optical_linewidth\Locked\1533\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p9mA_rec5.csv',
        r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\07_Optical_linewidth\Locked\1539p96\PN_locked_QDMLLD_49p7GHz_LaserCurrent_225mA_SampleTime_heatpad17p9mA_newfocus1539p96nm_rec1.csv']

    for locked_data_path in locked_paths:
        freq, noise = read_csv(locked_data_path, start_line=184)
        frequencies.append(freq)
        noises.append(noise ** 2)  # convert unit from Hz/sqrt(Hz) to Hz^2/Hz

    labels = ['w/o feedback, 1533 nm', 'w/ feedback, 1527 nm', 'w/ feedback, 1533 nm', 'w/ feedback, 1539 nm']

    plot_multiple_frequency_noise(freqs=frequencies, noises=noises, labels=labels, title='',
                                  save_dir=None)

def Linewidth_compare_journal():
    frequencies = []
    noises = []

    # Load free running data
    folder_path = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\CLEO2025\07_Optical_linewidth\Free_running\2024_11_26_free_running_IQ\rec7'
    file_name = 'IQ_free_running_QDMLLD_49p7GHz_LaserCurrent_225mA_span80M_RBW23k_wo_amplifier_rec8.csv'
    x_values, y_values = read_csv(os.path.join(folder_path, file_name), start_line=32, nr_points=100000, delimiter='\t')
    frequency_fr, noise_fr = cal_freq_noise_from_IQ(x_values, y_values, sampling_rate=37502094.028235, tau=1 / 13.536e6,
                                                    method='mlab_psd')
    frequencies.append(frequency_fr)
    noises.append(noise_fr)
    # np.save('offset_frequency', frequency_fr)
    # np.save('frequency_noise', noise_fr)

    # Locked data
    locked_data_dir = r'R:\Bao\Hybrid_comb\20250630_locking_FN\fit_linewidth'
    locked_paths = [
        r'FN_ch1_QDMLLD_locked_LaserCurrent_250mA_HeaterCurrent_12mA_laserTemp30deg_chipTemp30deg_afterWS_2.csv',
        r'FN_ch8_QDMLLD_locked_LaserCurrent_250mA_HeaterCurrent_12mA_laserTemp30deg_chipTemp30deg_afterWS_5.csv',
        r'FN_ch14_QDMLLD_locked_LaserCurrent_250mA_HeaterCurrent_12mA_laserTemp30deg_chipTemp30deg_afterWS_1.csv']

    for locked_data_name in locked_paths:
        locked_data_path = os.path.join(locked_data_dir, locked_data_name)
        freq, noise = read_csv(locked_data_path, start_line=184)
        frequencies.append(freq)
        noises.append(noise ** 2)  # convert unit from Hz/sqrt(Hz) to Hz^2/Hz

    labels = ['w/o feedback, 1533 nm', 'w/ feedback, ch1 1541.63 nm', 'w/ feedback, ch8 1536.13 nm', 'w/ feedback, ch14 1531.45 nm']

    plot_multiple_frequency_noise(freqs=frequencies, noises=noises, labels=labels, title='', freq_range=[10**2, 9 * 10**6],
                                  save_dir=r'R:\Bao\Hybrid_comb\20250630_locking_FN\fit_linewidth', save_filename='linewidth_comparison')

def plot_single_csv_file(file_path, start_line, nr_points, title=None, save_to_folder=None, save_filename=None, show_plot=True):
    x_values, y_values = read_csv(file_path, start_line=start_line, nr_points=nr_points)
    # x_values, y_values = get_average_values_from_dir(folder, start_line=start_line, nr_points=nr_points, delimiter='\t')
    plot_single_frequency_noise(x_values, y_values, title='Phase noise' if title is None else title,
                                font_size=22, save_path=os.path.join(save_to_folder, save_filename), show_plot=show_plot)


def plot_folder(folder_path, start_line=174, nr_points=1201, save_to_folder=None):
    csv_files = glob.glob(os.path.join(folder_path, '*.csv'), recursive=False)
    if not os.path.isdir(save_to_folder):
        os.mkdir(save_to_folder)
    for csv_file in csv_files:
        basename = os.path.basename(csv_file)
        file_name_list = basename.split('_')
        plot_title = f"Phase noise of {file_name_list[1]}-{file_name_list[-1][:-len('.csv')]}"
        plot_single_csv_file(file_path=csv_file, start_line=start_line, nr_points=nr_points, title=plot_title,
                             save_filename=basename[:-len('.csv')], save_to_folder=save_to_folder, show_plot=False)

def extract_channel_nr_from_filename(filename):
    basename = os.path.basename(filename)
    channel_nr = basename.split('_')[1][2:]
    return int(channel_nr)

def plot_intrinsic_linewidth_over_channel(data_folder, start_line=174, nr_points=1201, save_fig=False, show_fitting=False):
    """For JSTQE"""
    # from ch1 to ch14
    freq_centers = [194.46418, 194.56364, 194.6631 , 194.76256, 194.86202, 194.96148, 195.06094, 195.1604 ,
                    195.25986, 195.35932, 195.45878, 195.55824, 195.6577 , 195.75716]   # from waveshaper
    channel_freq_mapping = {i+1: freq_centers[i] for i in range(len(freq_centers))}
    channels = []
    channel_linewidth = []
    file_list = glob.glob(os.path.join(data_folder, '*.csv'), recursive=False)
    for csv_file in file_list:
        x_values, y_values = read_csv(csv_file, start_line=start_line, nr_points=nr_points)
        ch_id = extract_channel_nr_from_filename(csv_file)
        channels.append(ch_id)
        channel_linewidth.append(estimate_intrinsic_linewidth(freq_Hz=x_values, PSD_Hz_per_sqrt_Hz=y_values,
                                                              f_min=1e6, f_max=10e6, show_plot=show_fitting, plot_title=f'FN of channel {ch_id}'))
    freqs = np.asarray([channel_freq_mapping[ch] for ch in channels])
    from myutils.unit_converter import freq2wl
    wls = freq2wl(freqs)
    for ch, wl, linewidth in zip(channels, wls, channel_linewidth):
        print(f"Channel {ch}, wl: {wl:.2f} nm, linewidth: {linewidth/1e3:.2f} kHz")

    fig, ax = plt.subplots(figsize=(3.2, 1.7))

    ax.plot(freqs, np.asarray(channel_linewidth)/1e3, 'o', markersize=3)
    ax.set_xlabel('Frequency [THz]')

    # ax.plot(c/(np.asarray(freqs)*1e12)*1e9, np.asarray(channel_linewidth)/1e3, 'o', makersize=5)
    # ax.set_xlabel('Wavelength [nm]')

    ax.set_ylabel('Intrinsic linewidth [kHz]')
    ax.set_ylim([25, 150])
    # Set y-axis ticks to explicitly show the range boundaries
    ax.set_yticks(np.linspace(25, 150, 5))
    ax.grid(True, which='major')

    plt.tight_layout()
    if save_fig:
        plt.savefig(os.path.join(data_folder, 'IntrLinwidth_vs_freq.png'), transparent=False, dpi=300)
    plt.show()



if __name__ == '__main__':
    # Linewidth_compare_CLEO_2025()
    # plot_intrinsic_linewidth_over_channel(data_folder=r'R:\Bao\Hybrid_comb\20250630_locking_FN\fit_linewidth', save_fig=True)
    Linewidth_compare_journal()