# WaveShaper Presets (*.wsp) file
# (folder: C:\Users\<USER>\AppData\Roaming\WaveManager\wsp)
# Absolute Frequency (THz), Attenuation (dB), Phase (Rad) and Port
#   - Frequency (THz):
#   - Attenuation (dB): 0 - 40 dB, block state when attenuation > 40 dB
#   - Phase (rad): 0 - 2*pi
#   - Port: The alpha-numeric port combination (e.g. A-1) is referenced by a sequential port number (see manual p43). Block is signified by port 0.
#           e.g., Input  |  Output  |  Port     (for 4x16 configuration)
#                   A    |    1     |   1
#                  ...   |   ...    |  ...
#                   A    |    16    |   16
#                   B    |    1     |   17
import numpy as np
import os
import matplotlib.pyplot as plt

from myutils.unit_converter import wl2freq, freq2wl


class WSPGenerator:
    def __init__(self, block_by_default=True):
        # Waveshaper specs
        self.start_freq_THz = 191.1     # 1526.0 nm
        self.end_freq_THz = 196.46      # 1568.7 nm
        self.freq_res_THz = 0.001

        self.attn_block_dB = 60

        # Init array
        self.freqs_THz = np.arange(self.start_freq_THz, self.end_freq_THz + self.freq_res_THz, self.freq_res_THz)
        self.attenuations_dB = np.ones_like(self.freqs_THz, dtype=float) * self.attn_block_dB if block_by_default \
            else np.zeros_like(self.freqs_THz, dtype=float)
        self.phases_rad = np.zeros_like(self.freqs_THz, dtype=float)
        self.channels_int = np.zeros_like(self.freqs_THz, dtype=int)

    def generate_interleaver_spectrum(self, centers, window_THz=0.012, attenuation=0.0, output_channel=1):
        """
        centers: list of center frequencies (THz)
        window_size: full width of each passband (THz)
        phase: phase to apply in passbands (rad)
        """
        assert isinstance(output_channel, int)

        for i, center in enumerate(centers, start=1):
            self.pass_one_tone(center_freq_THz=center, bandwidth_THz=window_THz, attenuation=attenuation, output_channel=output_channel)

        return self.freqs_THz, self.attenuations_dB, self.phases_rad, self.channels_int

    def save_to_file(self, path):
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'w') as f:
            for v, a, p, c in zip(self.freqs_THz, self.attenuations_dB, self.phases_rad, self.channels_int):
                f.write(f"{v:.3f}\t{a:.3f}\t{p:.3f}\t{c}\n")
        print(f'File "{path}" has been generated.')

    def generate_centers(self, offset_THz, FSR_THz=0.05):
        centers = np.arange(self.start_freq_THz + offset_THz, self.end_freq_THz + FSR_THz, FSR_THz)
        if offset_THz > 0:
            compensate = np.arange(self.start_freq_THz + offset_THz - FSR_THz, self.start_freq_THz - FSR_THz, -FSR_THz)[::-1]
            centers = np.concatenate((compensate, centers))
        valid_centers = centers[(centers >= self.start_freq_THz) & (centers <= self.end_freq_THz)]
        return valid_centers

    def remove_spectrum(self, lower_lim_THz, upper_lim_THz):
        """Apply on the generated spectrum"""
        self.attenuations_dB = np.where((self.freqs_THz >= lower_lim_THz) & (self.freqs_THz <= upper_lim_THz),
                                        self.attn_block_dB, self.attenuations_dB)

    def pass_spectrum(self, lower_lim_THz, upper_lim_THz, output_channel=1):
        """Apply on the generated spectrum"""
        mask = (self.freqs_THz >= lower_lim_THz) & (self.freqs_THz <= upper_lim_THz)
        self.attenuations_dB = np.where(mask, self.attenuations_dB, self.attn_block_dB)
        self.channels_int[mask] = output_channel

    def pass_one_tone(self, center_freq_THz, bandwidth_THz, attenuation=0., output_channel=1):
        lower = center_freq_THz - bandwidth_THz / 2
        upper = center_freq_THz + bandwidth_THz / 2
        mask = (self.freqs_THz >= lower) & (self.freqs_THz <= upper)
        self.channels_int[mask] = output_channel
        self.attenuations_dB[mask] = attenuation

    def pass_only_one_tone(self, center_freq_THz, bandwidth_THz, attenuation=0., output_channel=1):
        self.pass_one_tone(center_freq_THz=center_freq_THz, bandwidth_THz=bandwidth_THz, attenuation=attenuation,
                           output_channel=output_channel)
        self.pass_spectrum(lower_lim_THz=center_freq_THz - bandwidth_THz/2,
                           upper_lim_THz=center_freq_THz + bandwidth_THz/2, output_channel=output_channel)

    def pass_centers_one_by_one(self, center_freqs_THz, bandwidth_THz, attenuation=0., output_channel=1,
                                save_folder=None, preview=True, generate_all_file_at_once=False):
        for i, center in enumerate(center_freqs_THz):
            self.pass_only_one_tone(center_freq_THz=center, bandwidth_THz=bandwidth_THz, attenuation=attenuation,
                                    output_channel=output_channel)
            if preview:
                self.preview_spectrum()
            if save_folder:
                # self.save_to_file(path=os.path.join(save_folder, f'{i+1:03}_{center:.3f}THz_BW-{bandwidth_THz*1e3}GHz.wsp'))
                self.save_to_file(path=os.path.join(save_folder, f'{i+1:03}_BW{bandwidth_THz*1000}GHz.wsp'))
            if not generate_all_file_at_once:
                print(
                    f'[INFO] {i + 1} - Pass channel at {center:.3f} THz with a bandwidth of {bandwidth_THz * 1e3:.1f} GHz')
                while True:
                    cmd = input("Continue with the next? [y/n]> ").strip()
                    if cmd.lower() == 'n':
                        exit()
                    elif cmd == 'y':
                        break
                    else:
                        continue

    def all_pass(self, output_channel=1):
        self.attenuations_dB = np.zeros_like(self.attenuations_dB)
        # self.attenuations_dB = np.ones_like(self.attenuations_dB) * 4   # FIXME: highest power when it is 4, wtf?
        self.channels_int = output_channel * np.ones_like(self.channels_int)

    def all_block(self):
        self.attenuations_dB = np.ones_like(self.attenuations_dB) * self.attn_block_dB
        self.channels_int = np.zeros_like(self.channels_int)

    def preview_spectrum(self):
        """
        Plot attenuation and phase spectrum.
        Frequencies with channel > 0 are shown with 0 dB attenuation; others at -50 dB (blocked).
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(6, 4), sharex=True)
        fig.suptitle("Preview", fontsize=12)

        # Plot Attenuation
        attn = self.attenuations_dB.copy()
        # attn = np.where(self.channels_int > 0, -attn, -self.attn_block_dB)
        # ax1.plot(self.freqs_THz, np.where(attn < -self.attn_block_dB, --self.attn_block_dB, attn), 'r-', linewidth=0.5)
        ax1.plot(self.freqs_THz, -attn, 'r-', linewidth=0.5)
        ax1.set_ylabel("Attn (dB)")
        ax1.set_ylim([-55, 5])
        ax1.grid(True)

        # Plot Phase
        ax2.plot(self.freqs_THz, self.phases_rad, 'r-', linewidth=0.5)
        ax2.set_ylabel("Phase (Rad)")
        ax2.set_xlabel("Frequency (THz)")
        ax2.set_ylim([-0.5, 0.5])
        ax2.grid(True)

        plt.tight_layout()
        plt.show()


if __name__ == '__main__':
    QDMLLD_FSR_THz = 0.04973
    generator = WSPGenerator(block_by_default=True)

    # start_wl, end_wl = 1529.5, 1542   # 16 tones
    # start_wl, end_wl = 1530.5, 1542  # 15 tones
    start_wl, end_wl = 1531, 1542   # 14 tones
    # start_wl, end_wl = 1527.5, 1542   # for linewidth measurement
    centers = generator.generate_centers(offset_THz=0.082, FSR_THz=QDMLLD_FSR_THz * 2)
    QDMLLD_tones = np.array([ct for ct in centers if ct > wl2freq(end_wl) and ct < wl2freq(start_wl)])
    print(f"{QDMLLD_tones = }")
    print(f"{QDMLLD_tones + 0.0020577444584539625 = }")
    print(f"{freq2wl(QDMLLD_tones) - 0.016312620000007882 = }")

    # Tx side
    # generator.generate_interleaver_spectrum(QDMLLD_tones, attenuation=0, window_THz=0.03)
    # generator.preview_spectrum()
    # generator.save_to_file(path=r'C:\Users\<USER>\AppData\Roaming\WaveManager\wsp\QDMMLD_interleave.wsp')

    # Rx side
    generator.pass_centers_one_by_one(center_freqs_THz=QDMLLD_tones, bandwidth_THz=0.09, attenuation=0.,
                                      output_channel=2,
                                      save_folder=r'C:\Users\<USER>\AppData\Roaming\WaveManager\wsp',
                                      preview=True, generate_all_file_at_once=True)

    # Debug, all pass
    # generator.all_pass(output_channel=1)
    # generator.preview_spectrum()
    # generator.save_to_file(path=r'C:\Users\<USER>\AppData\Roaming\WaveManager\wsp\all_pass.wsp')


    # LO wavelength
    # 1: 1541.619, 194.4660 (32 b2b, 16 b2b, 16 87km, 32 87km<==)
    # 2: 1540.834, 194.5650 (32 87km, 16 87km, 16 b2b, 32 b2b)
    # 3: 1540.042, 194.6652 (32 b2b, 16 b2b, 16 87km, 32 87km)
    # 4: 1539.260, 194.7640  (32 87km, 16 87km, 16 b2b, 32 b2b)
    # 5: 1538.470, 194.8640 (32 b2b, 16 b2b, 16 87km, 32 87km)
    # 6: 1537.689, 194.9630 (32 87km, 16 87km, 16 b2b, 32 b2b)
    # 7: 1536.901, 195.0630 (32 b2b, 16 b2b, 16 87km, 32 87km)
    # 8: 1536.121, 195.1620 (32 87km, 16 87km, 16 b2b, 32 b2b)
    # 9: 1535.342, 195.2610 (32 87km, 16 87km, 16 b2b, 32 b2b) -> 32 87km
    # 10: 1534.556, 195.3610 (32 b2b, 16 b2b, 16 87km, 32 87km)
    # 11: 1533.779, 195.4600 (32 87km, 16 87km, 16 b2b, 32 b2b)
    # 12: 1532.995, 195.5600 (32 b2b, 16 b2b, 16 87km, 32 87km)
    # 13: 1532.219, 195.6590 (32 87km, 16 87km, 16 b2b, 32 b2b)
    # 14: 1531.436, 195.7590 (16 & 32 QAM)
