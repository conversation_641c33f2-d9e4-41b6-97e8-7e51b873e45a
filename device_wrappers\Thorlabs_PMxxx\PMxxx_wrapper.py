import anyvisa
import numpy as np
import matplotlib.pyplot as plt


class PMxxx:
    def __init__(self, address=None):
        self._auto_range = None

        self.instr = None
        self.connect_device(address)
        self.reset()
        self.init_device()

    def reset(self):
        self.instr.write("*RST")

    def connect_device(self, address):
        try:
            devices_list = anyvisa.AnyVisa.FindResources("USB?*::INSTR")
            print(f"Found {len(devices_list)} USB devices:")

            for i, device in enumerate(devices_list):
                if address is not None:
                    if address not in str(device) or address != str(device):
                        continue
                try:
                    self.instr = device
                    self.instr.open()

                    try:
                        identity = self.instr.query("SYST:SENS:IDN?").strip()
                        print(f"  - Connect to device at {device}: {identity}")
                    except:
                        print(f"Could not query device {i+1}, skipping")
                        self.instr.close()

                except Exception as e:
                    print(f"Failed to connect to device {i+1}: {str(e)}")
            if self.instr is None:
                raise Exception("Failed to connect to any device")
        except Exception as e:
            print(f"Failed to connect to device: {str(e)}")

    def init_device(self):
        self.instr.write('SENS:FREQ:MODE CW')
        # self.instr.write(f"SENS:CORR:WAV 1550")
        self.instr.write("SENS:POW:UNIT dBm")
        # self.instr.write("SENS:AVER:1")
        self.instr.write("CONF:POW")

    def set_average(self, value):
        assert isinstance(value, int)
        self.instr.write(f"SENS:AVER: {value}")

    @property
    def wavelength(self):
        return self.instr.query(f"SENS:CORR:WAV?")

    @wavelength.setter
    def wavelength(self, value):
        assert isinstance(value, (int, float))
        self.instr.write(f"SENS:CORR:WAV {float(value):.2f}")

    @property
    def auto_range(self):
        return self.instr.query("SENS:RANGE:AUTO?")

    @auto_range.setter
    def auto_range(self, value: bool):
        if value:
            self._auto_range = True
            self.instr.write("SENS:RANGE:AUTO ON")
        else:
            self._auto_range = False
            self.instr.write("SENS:RANGE:AUTO OFF")

    def get_power_range(self):
        return self.instr.query(f"SENS:POW:RANG:UPPer?")

    def set_power_range(self, value_W):
        self.instr.write(f"SENS:POW:RANG:UPPer {value_W}")

    def close(self):
        self.instr.close()

    def get_power(self):
        try:
            power = float(self.instr.query("MEAS:POW?").strip())
            return power
        except Exception as e:
            print(f"Error reading from instrument: {str(e)}")
            return np.nan
        

if __name__ == "__main__":
    try:
        pm = PMxxx()

        pm.wavelength = 1550
        pm.auto_range = False
        pm.set_power_range(value_W=3.9e-3)  # 390 nW --> 39 mW  @ 1550 nm
        print(f"New power range: {pm.get_power_range()}")
        
        pm.set_average(2)
        
        current_pw = pm.get_power()
        print(f"Current power: {current_pw}")

    except Exception as e:
        print(f"Error: {str(e)}")
        pm.close()

