import sys
import time
import numpy as np
import platform
import os

if platform.system().lower() == 'windows':
    import clr
    clr.AddReference(r'mscorlib')
    from System.Text import StringBuilder
    from System import Int32
    from System.Reflection import Assembly
else:
    pass

class NewFocus6700:
    '''
    Class for Newfocus 67xx laser control through USB with proper
    usb driver installed.

    Args:
        key: laser DeviceKey
        id:  laser id
    Methods:
        Open: open laser instance
        Close: close laser instance
    Properties (Fetch/Set):
        self.connected: laser connection active or no
        self.output: ON/OFF output state of the laser
        self.lbd :float: laser wavelength in nm
        self.current :float: laser current in A
        self.scan_limit :[float, float]: DC scan limit in nm
        self.scan_speed :float: DC scan speed in nm
        self.scan :bool: dc scan status
        self.beep :bool: set/disabel beep
        self.error :(read only): fetch error laser and wipe
        self.identity :(read only): fetch laser identity
    Utilities:
        self._open: flag if opening of laser successful
        self._dev : laser usb socket
        self._buff : buffer reading the laser status
        self._is_changing_lbd : track if wavelength is still
                                changing after the user set a
                                wavelength
        self._is_scaning : track in background if the scan is
                           still ongoing

    Example:
        import time
        import numpy as np
        import matplotlib.pyplot as plt
        from NewFocus6700 import NewFocus6700

        idLaser = 4106
        DeviceKey = '6700 SN10027'
        laser = NewFocus6700(id =idLaser, key = DeviceKey)
        laser.connected = True
        old_lbd = laser.lbd
        print(f'Laser wavelength: {old_lbd}nm')
        laser.scan_limit = [1520, 1550]
        laser.scan_speed = 10
        laser.lbd = laser.scan_limit[0]
        print('waiting until laser parked at correct lbd')
        while laser._is_changing_lbd:
            time.sleep(0.25)
        print(f'Current wavelength: {laser.lbd}nm')
        print('Now turning on the laser')
        laser.output = True
        t = np.array([])
        lbd = np.array([])
        print('Starting scan')
        laser.scan = True
        while laser._is_scaning:
            pass
        print('Finished scanning... now turning off the laser')
        laser.output = False
        print('All Done!')
    '''

    def __init__(self, **kwargs):
        super(NewFocus6700, self).__init__()
        #Load usb ddl Newport
        try:
            dllpath = r'C:\Program Files\New Focus\New Focus Tunable Laser Application\Bin'
            Assembly.LoadFile(os.path.join(dllpath, 'UsbDllWrap.dll'))
            clr.AddReference(r'UsbDllWrap')
            import Newport
            self._dev = Newport.USBComm.USB()
            print(f"[INFO] Connected to New Focus 6700 laser.", end='')

        except Exception as err:
            print(err)
            self._dev = None
        # Laser state
        self._open = False
        self._DeviceKey = kwargs.get('key', None)
        self._idLaser = kwargs.get('id', 4106)
        # Laser properties
        self._lbd = '0'
        self._cc = 0
        self._power = 0
        self._scan_lim = []
        self._scan_speed = 0
        self._pzt_gain = 0
        self._scan = 0
        self._beep = 0
        self._output = 0
        self._is_scaning = False
        # self._is_changing_lbd = False
        self._no_error = '0,"NO ERROR"'
        self._haserr = False
        # Miscs
        self._buff = StringBuilder(64)
        self._err_msg = ''

    # -- Decorators --
    # ---------------------------------------------------------
    def Checkopen(fun):
        def wrapper(*args, **kwargs):
            self = args[0]
            # if self._open and self._DeviceKey:
            if self._open and self._DeviceKey:
                out = fun(*args, **kwargs)
                return out
            else:
                pass
        return wrapper

    # -- Methods --
    # ---------------------------------------------------------
    def Query(self, command):
        self._buff.Clear()
        self._dev.Query(self._DeviceKey, command , self._buff)
        return self._buff.ToString()


    # -- Properties --
    # ---------------------------------------------------------

    def query_connection_state(self):
        """
        Identify GPIB device.

        returns:
            str: The response from an ``*IDN?`` GPIB query.
        """
        return self._open

    def connect_laser(self, value):
        """
        Connect the laser controller.

        inputs:
            str: true or false
        """
        if value:
            if self._DeviceKey:
                while True:
                    out = self._dev.OpenDevices(self._idLaser, True)
                    tab = self._dev.GetDeviceTable()
                    #empty buffer
                    out = self._dev.Read(self._DeviceKey, self._buff)
                    # ipdb.set_trace()
                    while not (out == -1 or out == -2 or out == int("-2")):
                        out = self._dev.Read(self._DeviceKey, self._buff)
                        print('Empyting the buffer: {}'.format(out))
                        time.sleep(0.5)
                    idn = self.identity
                    if not idn == "":
                        print("\nLaser connected: {}".format(idn))
                        break
                    else:
                        print('Ok reconection try')
                        self._dev.CloseDevices()
                        time.sleep(0.2)

                    self.error
                    self._open = True
        else:
            self._dev.CloseDevices()
            self._open = False

    def query_output_state(self):
        """
        Query output state of the laser.

        returns:
            str: true or false
        """
        command = 'OUTPut:STATe?'
        self._output = self.Query(command)
        return self._output

    def set_output_state(self, value):
        """
        Set output state of the laser.

        inputs:
            str: true or false
        """
        command = "OUTPut:STATe {}".format(int(value))
        self.Query(command)
        self._output = value
        time.sleep(10)

    def query_lbd(self):
        """
        Query the current wavelength of the laser.

        returns:
            str: wavelength of the laser
        """
        command = 'SENSe:WAVElength?'
        self._lbd = self.Query(command)
        return self._lbd

    def set_lbd(self, Lambda):
        """
        Set the wavelength of the laser.

        inputs:
            float: target wavelength of the laser
        """
        self._targetlbd = Lambda
        self.Query('OUTP:TRACK 1')
        command = 'SOURCE:WAVE {}'.format(Lambda)
        self.Query(command)
        self._lbd = Lambda

    def query_laser_current(self):
        """
        Query the bias current of the laser diode.

        returns:
            str: laser current (mA)
        """
        command = 'SOUR:CURR:DIOD?'
        self._cc = self.Query(command)
        return self._cc

    def set_laser_current(self, current_value):
        """
        Set the bias current of the laser diode.

        inputs:
            float: targeted laser current (mA)
        """
        command = 'SOUR:CURR:DIOD {}'.format(current_value)
        self.Query(command)
        self._cc = current_value

    def query_out_power(self):
        """
        Query laser diode output power.

        returns:
            float: current laser output power (mW)
        """
        command = 'SOURce:POWer:DIODe?'
        self._power = self.Query(command)
        return self._power

    def set_out_power(self, power_mW):
        """
        Set laser diode output power.

        inputs:
            float: targeted laser output power (mW)
        """
        command = 'SOURce:POWer:DIODe {}'.format(power_mW)
        self.Query(command)
        power_current = self.Query('SOURce:POWer:DIODe?')
        return print('Laser power = {} mW'.format(power_current))

    def query_wl_scan_limit(self):
        """
        Query the wavelength scan limits.

        returns:
            list: lower and upper boundaries of the wavelength scan limits (nm)
        """
        wl_start_cmd = 'SOUR:WAVE:START?'
        wl_stop_cmd = 'SOUR:WAVE:STOP?'
        self._scan_lim = [self.Query(wl_start_cmd), self.Query(wl_stop_cmd)]
        return self._scan_lim

    def set_wl_scan_limit(self, wl_scan_limit_value):
        """
        Set the wavelength scan limits.

        inputs:
            list: lower and upper boundaries of the wavelength scan limits (nm)
        """
        wl_start = wl_scan_limit_value[0]
        wl_stop = wl_scan_limit_value[1]
        wl_start_cmd = 'SOUR:WAVE:START {}'.format(wl_start)
        self.Query(wl_start_cmd)
        wl_stop_cmd = 'SOUR:WAVE:STOP {}'.format(wl_stop)
        self.Query(wl_stop_cmd)
        self._scan_lim = wl_scan_limit_value

    def query_pzt_gain(self):
        """
        PZT gain query.

        returns:
            str: pzt gain information (20x or 1x)
        """
        query_pzt_gain_cmd = 'SOURce:VOLTage:PZTGAIN?'
        pzt_gain_value = self.Query(query_pzt_gain_cmd)
        self._pzt_gain = pzt_gain_value
        if pzt_gain_value == '1':
            return print('High PZT gain (20x)')
        else:
            return print('Low PZT gain (1x)')

    def set_pzt_gain(self,pzt_gain_value):
        """
        set PZT gain.

        inputs:
            bool: pzt gain value (1 -> 20x high gain, 0 -> 1x low gain)
        """
        set_pzt_gain_cmd = 'SOURce:VOLTage:PZTGAIN {}'.format(int(pzt_gain_value))
        self.Query(set_pzt_gain_cmd)
        self._pzt_gain = pzt_gain_value

    def query_scan_speed(self):
        """
        Query the wavelength scan speed of the laser.

        returns:
            str: wavelength scan speed of the laser (nm/s)
        """
        self._scan_speed_forward = self.Query('SOUR:WAVE:SLEW:FORW?')
        self._scan_speed_return = self.Query('SOUR:WAVE:SLEW:RET?')
        return self._scan_speed_forward, self._scan_speed_return
    
    def get_scan_config(self):
        return self.Query('SOURce:WAVE:SCANCFG?')

    def set_wl_scan_speed(self, wl_scan_speed_forward, wl_scan_speed_return=None):
        """
        Set the wavelength scan speed of the laser.

        inputs:
            float: wavelength scan speed of the laser (nm/s)
        """
        if wl_scan_speed_return is None:
            wl_scan_speed_return = wl_scan_speed_forward
        wl_scan_speed_cmd = 'SOUR:WAVE:SLEW:FORW {}'.format(wl_scan_speed_forward)
        self.Query(wl_scan_speed_cmd)
        set_wl_scan_return_speed = 'SOUR:WAVE:SLEW:RET {}'.format(wl_scan_speed_return)
        self.Query(set_wl_scan_return_speed)
        self._scan_speed = wl_scan_speed_forward

    def scan(self):
        """
        Query the actual number of scans completed.

        returns:
            float: actual number of scans completed
        """
        word = 'SOUR:WAVE:DESSCANS?'
        self._scan = self.Query(word)
        return self._scan

    def start_wl_scan_process(self, value):
        """
        Start the wavelength scan process.

        inputs:
            bool: true -> start wavelength scan process, false -> stop wavelength scan process
        """
        self.Query('SOUR:WAVE:DESSCANS 1') # set desired number of scan
        self._scan = value
        if self._scan:
            self.Query("OUTPut:SCAN:START")
        else:
            self.Query("OUTPut:SCAN:STOP")

    def is_operation_complete(self):
        return self.Query('*OPC?')

    def query_pzt_voltage(self):
        """
        Query of the PZT voltage (0 - 100%).

        returns:
            float: PZT voltage (0 - 100%)
        """
        query_pzt_volt_cmd = 'SOUR:VOLT:PIEZ?'
        self._pzt = self.Query(query_pzt_volt_cmd)
        return self._pzt

    def set_pzt_voltage(self, pzt_voltage):
        """
        Set the PZT voltage (0 - 100%).

        inputs:
            float: PZT voltage (0 - 100%)
        """
        set_pzt_volt_cmd = 'SOUR:VOLT:PIEZ {}'.format(pzt_voltage)
        self.Query(set_pzt_volt_cmd)
        self._pzt = pzt_voltage

    def beep(self):
        """
        Query beeper state.

        returns:
            bool: true or false
        """
        beep_cmd = 'BEEP?'
        self._beep = self.Query(beep_cmd)
        return self.beep

    def set_beep_state(self, value):
        """
        Set beeper state.

        inputs:
            bool: true or false
        """
        set_beep_state_cmd = 'BEEP '.format(int(value))
        self.Query(set_beep_state_cmd)
        self._beep = value

    def identity(self):
        """
        Identification query.

        returns:
            str: identification string
        """
        query_idn_cmd = "*IDN?"
        self._id = self.Query(query_idn_cmd)
        return self._id
    
    def reset(self):
        """
        Reset the laser.
        """
        self.Query('*RST')
        time.sleep(10)

    def error(self):
        """
        Error query.

        returns:
            str: error string
        """
        query_err_cmd = 'ERRSTR?'
        self._error = ''
        err = self.Query(query_err_cmd)
        return err
    
    def wait_for_tracking(self):
        while int(self._is_changing_lbd()):
            time.sleep(0.05)

    def has_error(self):
        """
        query status.

        returns:
            str: status information (has error or has no error)
        """
        word = '*STB?'
        dum = self.Query(word)
        if dum =='128': self._haserr = True
        if dum == '0': self._haserr = False
        return self._haserr

    def _is_changing_lbd(self):
        """
        check if the wavelength is changing.

        returns:
            bool: wavelength track mode
        """
        return self.Query('OUTP:TRACK?')

    def set_track_mode(self, mode):
        assert isinstance(mode, bool)
        mode = 'ON' if mode else 'OFF'
        self.Query(f'OUTPut:TRACk {mode}')
    
    def disconnect_laser(self):
        self._dev.CloseDevices()
        self._open = False
        print("[INFO] New Fucus 6700 laser connection closed.")


if __name__ == '__main__':
    tlb6700 = NewFocus6700(id=4106, key='6700 SN70006')
    tlb6700.connect_laser(True)
    tlb6700.set_lbd(1550)
    tlb6700.set_out_power(3)
    tlb6700.set_output_state(value=True)
    inpt = input("Press Enter to continue...")
    tlb6700.set_output_state(value=False)
