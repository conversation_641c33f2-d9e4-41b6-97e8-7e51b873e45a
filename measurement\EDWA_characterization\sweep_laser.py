import traceback
import numpy as np
import time
import matplotlib.pyplot as plt
from tqdm import tqdm

from device_wrappers.Newfocus_TLB6700 import NewFocus_TLB6700_wrapper
from device_wrappers.Thorlabs_PMxxx.PMxxx_wrapper import PMxxx


class ReflectivityCharacterization:
    def __init__(self):
        self.start_wl = 1520
        self.end_wl = 1550
        self.fwd_scan_speed = 20.0
        self.bwd_scan_speed = 20.0
        self.laser_power = 3

        self.ecl = NewFocus_TLB6700_wrapper.NewFocus6700(id=4106, key='6700 SN70006')
        self.ecl.connect_laser(True)
        self.init_ecl()

    def init_ecl(self):
        self.ecl.reset()
        # self.ecl.set_wl_scan_speed(wl_scan_speed=20.0)  # influence the wl changing speed, 20 nm/s is the max.
        self.ecl.set_wl_scan_speed(wl_scan_speed_forward=self.fwd_scan_speed, wl_scan_speed_return=self.bwd_scan_speed)
        self.ecl.set_wl_scan_limit(wl_scan_limit_value=[self.start_wl, self.end_wl])
        self.ecl.set_lbd(Lambda=self.start_wl)
        self.ecl.set_pzt_voltage(0)
        self.ecl.set_out_power(power_mW=self.laser_power)

    def cal_scan_time(self):
        # wl_lims  = self.ecl.query_wl_scan_limit()
        # print(f"Wavelength limits: {wl_lims}")    # empty
        wl_span = self.end_wl - self.start_wl
        # frd_speed, bwd_speed = self.ecl.query_scan_speed()    # empty
        # scan_config = self.ecl.get_scan_config()
        # print(f"Scan config: {scan_config}")
        scan_time = wl_span / self.fwd_scan_speed
        if self.bwd_scan_speed != 0:
            scan_time += wl_span / self.bwd_scan_speed  

        return scan_time

    def start_sweeping(self):
        scan_time = self.cal_scan_time() 
        print(f"Scan time: {scan_time}")

        self.ecl.set_output_state(value=True)
        self.ecl.start_wl_scan_process(True)    # the scan always go forth and back. Back scan is faster than forward.
        time.sleep(scan_time + 3)

    def close(self):
        self.ecl.set_output_state(value=False)


if __name__ == '__main__':
    try:
        reflectivity_char = ReflectivityCharacterization()
        reflectivity_char.start_sweeping()
    except:
        print(traceback.format_exc())
    finally:
        reflectivity_char.close()