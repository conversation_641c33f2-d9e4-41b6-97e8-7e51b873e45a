import numpy as np
import os
import matplotlib.pyplot as plt

from ripple.field import Trans<PERSON><PERSON>ield
from ripple.field_distribution import GaussianDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens, SingleSurface
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.geometry import rotate_vector
from ripple.utils.optics import cal_exit_angle
from ripple.utils.sim_helpers import run_sim

# TODO: Check the influence of the initial parameters

# info
c_wl = 1.48

# Mode field radius
# d4sigma:  x: 1.67, y:  1.3
source_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=2.59, w0y=1.64),
                               z0=0, refractive_index=1.53, wavelength=c_wl)
target_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=15, w0y=15),
                               z0=300, refractive_index=1, wavelength=c_wl)

# monitor_yz = FieldMonitor(monitor_type='yz', position=0, saving_path=None)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_power_per_z=True,
                          record_beam_radius_per_z=True)

lens1 = SingleSurface(  # Coupling: 0.9521318348103909
            surface_type='ConoidalAsphericSurface',
            surface_parameters={'x0': 0, 'y0': 0, 'z0': 108.49224671163654,
                                'rho_x': -0.026869235481505747, 'kappa_x': -0.5183255037659887,
                                'rho_y': -0.02778926060550792, 'kappa_y': -0.5029252146791529},
            max_radius=125/2, material_side=-1, refractive_index=1.53, priority=2)

scene = CouplingScene(input_field=source_field, target_field=target_field,
                      background_material_index=1.,
                      optical_structures=[lens1],
                      sim_size_xy=[200, 200],
                      # sim_z_end=target_center_z + 10,
                      wavelength_sampling_xy=4,
                      material_wavelength_sampling_z=4,
                      background_wavelength_sampling_z=2,
                      solver='wpm',
                      # solver='bvwpm', solver_pars={'polarization': 'x'},
                      monitors=[monitor_xz],
                      boundary_condition='PML', boundary_parameters={"boundary_thickness": [5]*2})


if __name__ == "__main__":
    if False:
        scene.preview(mode='xz', position=0)
        scene.preview(mode='yz', position=0)
        plt.show()

    if True:
        # current_dir = os.path.abspath(os.path.dirname(__file__))
        # save_dir = os.path.join(current_dir, 'default_fit')
        save_dir = None

        opt_pars = {lens1.name: {'Surface': {
            'surface': ['z0', 'kappa_x', 'kappa_y', 'rho_x', 'rho_y'],
        }}}

        run_sim(scene, optimize_structure=True, sim_result_dir=save_dir, opt_log_dir=save_dir,
                opt_pars=opt_pars, cal_coupling=True, show_plot=True)

    if False:
        plot_monitor_from_dir(dir_path=r'D:\WORK\02_Research\09_Hybrid_comb_packaging\03_Lens_design\04_QDMLLD_J5_to_SiN_B4_10cm\Design3_20deg\QDMLLD_lens_d3\forward', scene=scene)
        plt.show()

    if False:
        monitor_path = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\03_Lens_design\04_QDMLLD_J5_to_SiN_B4_10cm\Design3_20deg\QDMLLD_SiN__lenses_d4\1_monitor_xy_2024-10-24_14-08-57.pkl'
        monitor = FieldMonitor.load(path=monitor_path)

        ce = monitor.cal_overlap_integral(input_field=source_field, field_type='E', operation='abs', direction='reflected', debug=True)
        print(f"{ce = }")   # 0.0028328535 (4, 4, 2)

