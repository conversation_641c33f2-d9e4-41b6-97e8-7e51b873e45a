import time

import numpy as np
import traceback

from device_wrappers.AQ6370D_OSA import AQ6370D_wrapper
from device_wrappers.Newfocus_TLB6700 import NewFocus_TLB6700_wrapper

sensitivity_dict = {
0: 'NHLD',
1: 'NAUT',
2: 'MID',
3: 'HIGH1',
4: 'HIGH2',
5: 'HIGH3',
6: 'NORMAL'
}

def get_list(start, end, step=None, nr=None):
    if step is not None:
        return np.arange(start, end + step, step).astype(float)
    if nr is not None:
        return np.linspace(start, end, num=nr, endpoint=True).astype(float)


def dBm_to_mW(power_dBm):
    return 10.0 ** (power_dBm / 10.0)


def mW_to_dBm(power_mW):
    return 10.0 * np.log10(power_mW)


def sweep_wavelength_and_power(plot_spectrum=True):
    laser_config = {'wl_scan_limit': [1520, 1570]}
    wl_offset = -0.3
    attenuation = 1.090 # 10.998 # 25.997

    folder = r"D:\WORK\02_Research\10_EDWA_packaging\05_Experiment\EPFL_packaged_1ch_EDWA\3_gain_sweep\OSA"

    # init new focus laser
    tlb6700 = NewFocus_TLB6700_wrapper.NewFocus6700(id=4106, key='6700 SN70006')
    tlb6700.connect_laser(True)
    tlb6700.set_wl_scan_speed(wl_scan_speed=10.0)
    tlb6700.set_wl_scan_limit(wl_scan_limit_value=laser_config['wl_scan_limit'])
    tlb6700.start_wl_scan_process(value=False)

    # init OSA
    OSA = AQ6370D_wrapper.AQ6370D('GPIB0::5::INSTR')

    # Sweep wavelength and power
    try:
        output_state = False
        direction = 1
        for laser_power in dBm_to_mW(get_list(1.76, 10.42, nr=3))[::-1]:
            tlb6700.set_out_power(power_mW=laser_power)
            new_power = True

            for wl in get_list(1550 + wl_offset, 1570 + wl_offset, step=5)[::direction]:
                tlb6700.set_lbd(Lambda=wl)
                print(f"Wavelength: {wl}nm\n")
                OSA_config = {'centwlgth': wl, 'span': 5, 'rbw': 0.1, 'pts': 9001, 'sens': sensitivity_dict[3]}
                # print(f"Wavelength: {wl - wl_offset}nm\n")
                # OSA_config = {'centwlgth': wl - wl_offset, 'span': 5, 'rbw': 0.1, 'pts': 9001, 'sens': sensitivity_dict[3]}
                OSA.config_settings(config_value=OSA_config)
                time.sleep(10)

                if not output_state:
                    tlb6700.set_output_state(value=True)
                    output_state = True

                if new_power:
                    time.sleep(15)
                    new_power = False

                OSA.config_scan_type(scan_type='single')
                time.sleep(3)
                # OSA.save_trace(folder_path=folder, filename=f'wl{wl:.2f}nm_inputSetPower{laser_power:.4f}dBm_att{attenuation:.3f}dB')
                OSA.save_trace(folder_path=folder, filename=f'wl{wl:.2f}nm_inputSetPower{laser_power:.4f}dBm_att{attenuation:.3f}dB_output')
                OSA.plot_trace(freq=False)
            direction *= -1
    except:
        print(traceback.format_exc())
    finally:
        tlb6700.set_output_state(value=False)


def main():
    print(dBm_to_mW(power_dBm=-30))



if __name__ == '__main__':
    # TODO: Laser threshold 0.5 mA (-3 dBm), lower than that the power is not stable.

    # TODO: VOA 21.6 dB attenuation
    # print(mW_to_dBm([0.5]))    # set 12.3 dBm --> OSA: -5 dBm
    # print(dBm_to_mW(get_list(-3.15, 5.15, 4))[::-1])

    sweep_wavelength_and_power()

    # 16.98 mW --> -14.15
    # 6.61 --> -18.78
    # 2.57 --> -23.26
    # 1 --> -29.38

    # 2025-03-18
    # VOA: 25.997 dB attenuation
    # 1.4 (1.46 dBm) --> -30 dBm
    # 10.5 (10.21 dBm) --> ~ -20 dBm

    # VOA: 10.998 dB
    # 1.4 --> ~ -15 dBm
    # 4 --> ~ -10 dBm

    # VOA: 1.090 dB
    # 1.5 mW (1.76 dB) --> ~ -5 dBm
    # 11 mW (10.42 dB) --> ~ 5 dBm