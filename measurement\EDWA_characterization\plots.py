import os
import re
import numpy as np
import pandas as pd
import glob
import matplotlib.pyplot as plt
plt.rcParams['font.size'] = 14

from process_measurement.OSA.OSA_spectrum.plot_utils import read_file
from process_measurement.OSA.plot_EDWA_spectrum import compare_spectra


def find_peak(x, y):
    max_idx = np.argmax(y)
    max_x = x[max_idx]
    max_y = y[max_idx]
    return max_x, max_y


def extract_gain(x1, y1, x2, y2):
    max_x1, max_y1 = find_peak(x1, y1)
    max_x2, max_y2 = find_peak(x2, y2)

    assert np.isclose(max_x1, max_x2, atol=0.1), "The peaks of the two spectra are not at the same wavelength."

    return (max_x1 + max_x2)/2, max_y2 - max_y1


def get_output_file(input_file_name):
    return input_file_name[:-len('.csv')] + '_output.csv'


def extract_data_from_folder(folder_path, montage=False):
    all_files = glob.glob(folder_path + '/*.csv')
    wl_offset = 0.3

    df = pd.DataFrame(columns=['wavelength', 'input_peak_power', 'output_peak_power', 'gain', 'attenuation'])

    for file_path in all_files:
        filename = os.path.basename(file_path)
        if 'output' in filename:
            continue
        output_file_path = get_output_file(file_path)

        if montage:
            compare_spectra(file_path, output_file_path, file1_label='input', file2_label='output')

        # Extract values from filename using regex
        wavelength_match = re.search(r'wl(\d+\.\d+)nm', filename)
        wavelength = float(wavelength_match.group(1)) + wl_offset
        attenuation_match = re.search(r'att(\d+\.\d+)dB', filename)
        attenuation = float(attenuation_match.group(1)) if attenuation_match else np.nan

        max_x_in, max_y_in = find_peak(*read_file(file_path))
        max_x_out, max_y_out = find_peak(*read_file(output_file_path))

        assert np.isclose(max_x_in, max_x_out, atol=0.1), "The peaks of the two spectra are not at the same wavelength."

        peak_wl, peak_gain = (max_x_in + max_x_out) / 2, max_y_out - max_y_in

        temp_df = {
            'set_input_power': round(max_y_in / 5) * 5,
            'set_wavelength': wavelength,
            'actual_wavelength': peak_wl,
            'input_peak_power': max_y_in,
            'output_peak_power': max_y_out,
            'gain': max_y_out - max_y_in,
            'attenuation': attenuation
        }

        df = pd.concat([df, pd.DataFrame([temp_df])], ignore_index=True)

    return df


def plot_gain_vs_input(df):
    plt.figure(figsize=(10, 6))

    unique_wavelengths = df['set_wavelength'].unique()

    # Plot each wavelength with a different color
    for wavelength in unique_wavelengths:
        subset = df[df['set_wavelength'] == wavelength]

        subset = subset.sort_values('input_peak_power')

        plt.plot(subset['input_peak_power'], subset['gain'], 'o-', label=f'λ = {wavelength}')

    plt.title('Gain')
    plt.xlabel('Input signal power (dBm)')
    plt.ylabel('EDWA off-chip Gain (dB)')
    plt.grid(True, alpha=0.3)
    plt.legend(title='Wavelength')

    plt.tight_layout()
    plt.show()


def plot_output_power_vs_input(df):
    plt.figure(figsize=(10, 6))

    unique_wavelengths = df['set_wavelength'].unique()

    # Plot each wavelength with a different color
    for wavelength in unique_wavelengths:
        subset = df[df['set_wavelength'] == wavelength]

        subset = subset.sort_values('input_peak_power')

        plt.plot(subset['input_peak_power'], subset['output_peak_power'], 'o-', label=f'λ = {wavelength}')

    plt.title('Output power')
    plt.xlabel('Input signal power (dBm)')
    plt.ylabel('Output signal power (dBm)')
    plt.grid(True, alpha=0.3)
    plt.legend(title='Wavelength')

    plt.tight_layout()
    plt.show()


def plot_gain_vs_wavelength(df):
    plt.figure(figsize=(10, 6))

    unique_input_power = df['set_input_power'].unique()

    # Plot each wavelength with a different color
    unique_input_power_sorted = sorted(unique_input_power)
    for input_power in unique_input_power_sorted:
        subset = df[df['set_input_power'] == input_power]

        subset = subset.sort_values('set_wavelength')

        plt.plot(subset['set_wavelength'], subset['gain'], 'o-', label=f'P = {input_power:.2f}')

    plt.title('Gain over wavelength')
    plt.xlabel('Wavelength (nm)')
    plt.ylabel('Gain (dB)')
    plt.grid(True, alpha=0.3)
    plt.legend(title='Input power (dBm)')

    plt.tight_layout()
    plt.show()


def main():
    folder_path = r'D:\WORK\02_Research\10_EDWA_packaging\05_Experiment\EPFL_packaged_1ch_EDWA\3_gain_sweep\OSA'
    df = extract_data_from_folder(folder_path, montage=False)
    plot_gain_vs_input(df)
    plot_output_power_vs_input(df)
    plot_gain_vs_wavelength(df)


if __name__ == '__main__':
    main()
