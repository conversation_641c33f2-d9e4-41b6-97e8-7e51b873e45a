from __future__ import division
import os
import shutil
from os import listdir
from os.path import isfile, join
import numpy as np

log_filepath = ''
log_str = ''


# For measurement logs
def set_log_file_path(filedir, filename):
    global log_filepath
    if not os.path.isdir(filedir):
        os.mkdir(filedir)
    log_filepath = filedir + filename


def info(msg, mode='single'):
    assert isinstance(msg, str)
    assert mode in ['single', 'both']

    global log_filepath, log_str
    if mode == 'single':
        if log_filepath != '':
            append_message(msg)
        else:
            print(msg)
    elif mode == 'both':
        print(msg)
        append_message(msg)


def append_message(msg2write):
    global log_str
    try:
        log_str += msg2write + '\n'
    except MemoryError:
        with open(log_filepath, 'a') as logf:
            logf.write(log_str)
        log_str = msg2write + '\n'


def close_log_file():
    global log_filepath, log_str
    if log_filepath != '':
        try:
            with open(log_filepath, 'a') as logf:
                logf.write(log_str)
            print("Wrote into log file: {}".format(log_filepath))
        except AttributeError:
            print('Cannot wirte into file: {}'.format(log_filepath))
        finally:
            log_str = ''


def file_accessible(abs_filepath):
    """Check if the given file name already exists."""
    if os.path.isfile(abs_filepath):
        print("File ({}) exists!".format(abs_filepath))
        overwrite_permit = input("Do you want to overwrite it (y/n)? ")
        if overwrite_permit in ["yes", "Yes", "y", "Y"]:
            os.remove(abs_filepath)
            print("Writing into file: {}".format(abs_filepath))
            return True
        else:
            return False
    else:
        print("Created and writing into file: {}".format(abs_filepath))
        return True


def read_ct400_spectrum_from_txt(ct400_file_abspath):
    with open(ct400_file_abspath, 'r') as f:
        line_count = 0
        data = []
        for line in f:
            if line_count == 0:
                # meas_info = line
                print("Measurement info: \n{}".format(vars_list))
            elif line_count == 1:
                vars_list = line.rstrip().split('\t')
                print("Variable list: \n{}".format(vars_list))
            else:
                data.append(np.asarray(line.rstrip().split('\t')).astype(float))
            line_count += 1
        data = np.asarray(data)
        wl, de1, de4 = data[:, 1], data[:, 2], data[:, 3]
    return wl, de1, de4


def read_data_by_rows(fileabspath):
    data = []
    if not os.path.isfile(fileabspath):
        print("File: {} does not exist.".format(fileabspath))
    with open(fileabspath, 'r') as f:
        for line in f:
            line_data = line.rstrip().split('\t')
            data.append(np.asarray(line_data[1:]).astype(float))
    data[-1] = data[-1].astype(int)
    data[0] = data[0][0]
    data = np.asarray(data)
    return data


def save_osa_measurement(wl, pw, peaks, wloi, file_name):
    # file_name += ".txt"
    var_list = ['Wavelength(nm)', 'Optical_power(dBm)', 'Peak_indices']
    with open(file_name, 'w') as f_osa:
        f_osa.write("wloi\t{}\n".format(wloi))
        for i, data in enumerate([wl, pw, peaks]):
            form_str = "%s\t" + "%s\t" + "\n"
            f_osa.write(form_str % (var_list[i], '\t'.join(str(x) for x in data[:])))


def read_osa_noise_from_file(filepath):
    if not os.path.isfile(filepath):
        print("Cannot find file: {}".format(filepath))
        raise
    noise_data = []
    with open(filepath, 'r') as f:
        for line in f:
            noise_data.append(np.asarray(line.rstrip().split('\t')).astype(float))
    noise_data = np.asarray(noise_data)
    noise_wl, noise_floor = noise_data[:, 0], noise_data[:, 1]
    return noise_wl, noise_floor


def save_osa_noise_to_file(filepath, wl, noise):
    with open(filepath, 'w') as f:
        for i in range(len(wl)):
            f.write("{}\t{}\n".format(wl[i], noise[i]))


def list_all_txt_files_in_dir(dir_path):
    file_list = [f for f in listdir(dir_path) if isfile(join(dir_path, f))]
    return [tf for tf in file_list if tf[-4:] == '.txt']


def delete_all_plts(dir_path):
    file_list = [f for f in listdir(dir_path) if isfile(join(dir_path, f))]
    img_file = [tf for tf in file_list if tf[-4:] == '.png']
    for imgf in img_file:
        os.remove(dir_path + imgf)


def process_ops(input_filepath, out_filepath=None):
    with open(input_filepath, 'r') as opf:
        lines = opf.readlines()
    if len(lines) > 1:
        lines += '\n'
    headline = lines.pop(0)
    column_names = headline.rstrip().split('\t')
    wl_column = column_names.index('Desired_wavelength[nm]')
    smsr_column = column_names.index('SMSR[dB]')
    sm_column = column_names.index('single_mode')

    keep_indices = []
    line_list = []

    for n in range(len(lines)):
        line_list.append(lines[n].strip().split('\t'))
        if len(line_list[-1]) == len(column_names):
            keep_indices.append(n)

    line_list = np.asarray(line_list)
    wls = list(map(float, [line[wl_column] for line in line_list[keep_indices]]))

    check_id, final_indices = [], []
    check_id.extend(keep_indices)
    final_indices.extend(keep_indices)
    failed_op_wls = []
    failed_indices = []
    for n in check_id:
        current_wl = float(line_list[n][wl_column])
        repeated_indices = [wid for wid, wl in zip(keep_indices, wls) if wl == current_wl and wid != n]
        if len(repeated_indices) != 0 and (n in final_indices):
            repeated_indices.extend([n])
            max_smsr = max([float(ll[smsr_column]) for ll in line_list[repeated_indices]])
            if float(line_list[n][smsr_column]) != max_smsr:
                final_indices.remove(n)
        if (n in final_indices) and (line_list[n][sm_column] != 'True') and (len(line_list[n]) == len(column_names)):
            failed_indices.append(n)
            failed_op_wls.append(float(line_list[n][wl_column]))
            final_indices.remove(n)
        if (n in final_indices) and (len(line_list[n]) != len(column_names)):
            final_indices.remove(n)

    keep_lines = [ll for i, ll in enumerate(lines) if i in final_indices]
    if out_filepath is None:
        new_path = input_filepath.split('.')
        new_path[-2] += '_sorted'
        out_filepath = '.'.join(new_path)
    sort_and_save_ops(headline, keep_lines, output_filepath=out_filepath)

    failed_lines = [fl for i, fl in enumerate(lines) if i in failed_indices]
    failed_op_path = input_filepath.split('.')
    failed_op_path[-2] += '_failed'
    failed_filepath = '.'.join(failed_op_path)
    sort_and_save_ops(headline, failed_lines, output_filepath=failed_filepath, append=True)
    return np.asarray(failed_op_wls), min(wls), max(wls)


def sort_and_save_ops(headline, lines, output_filepath, append=False):
    lines.sort()
    if append and os.path.isfile(output_filepath):
        out_file = open(output_filepath, 'a')
    else:
        out_file = open(output_filepath, 'w')
        out_file.write(headline)
    for i, line in enumerate(lines):
        if i == len(lines) - 1:
            # TODO: test this part
            out_file.write(line[:-1])   # remove '\n' in the last line
        else:
            out_file.write(line)
    out_file.seek(-1, os.SEEK_END)
    out_file.close()


def get_failed_ops(lines):
    failed_ops = []
    for line in lines:
        line = line.strip().split('\t')
        if line[-1] != 'True':
            failed_ops.append(float(line[0]))
    return failed_ops


def move_files(keywords, dest_dir, output_dir):
    if not os.path.isdir(dest_dir):
        print("Cannot find directory: {}".format(dest_dir))
        return
    if not os.path.isdir(output_dir):
        os.mkdir(output_dir)
    file_list = list_all_txt_files_in_dir(dest_dir)
    for keyword in keywords:
        for f in file_list:
            if str(keyword) in f:
                shutil.move(dest_dir + f, output_dir + f)


def remove_files(keywords, dest_dir):
    """Delete files that contains ANY of the keywords."""
    if not os.path.isdir(dest_dir):
        print("Cannot find directory: {}".format(dest_dir))
        return
    file_list = list_all_txt_files_in_dir(dest_dir)
    for keyword in keywords:
        for f in file_list:
            if str(keyword) in f:
                os.remove(dest_dir + f)
                info("Removed file: {}".format(f), mode='both')


def load_ops_from_file(filepath):
    line_count = 0
    current_ops = []
    expected_wl = []
    print("Reading measurements from file {}".format(filepath))
    with open(filepath, 'r') as opf:
        for line in opf:
            line_count += 1
            if line_count == 1:
                column_names = line.rstrip().split('\t')
                wl_column = column_names.index('Actual_wavelength[nm]')
                ao_start_column = column_names.index('ao0_osa[mA]')
                ao_end_column = column_names.index('ao2_osa[mA]')
                try:
                    v_rsoa_column = column_names.index('v_rsoa[V]')
                except ValueError:
                    v_rsoa_column = None
            else:
                if len(line) != 0:
                    line_data = line.rstrip().split('\t')
                    expected_wl.append(float(line_data[wl_column]))
                    # [[ao0_1, ao1_1, ao2_1], [ao0_2, ao1_2, ao2_2]]
                    # Current unit: A
                    current_ops.append(np.asarray(line_data[ao_start_column:ao_end_column + 1]).astype(float) * 1E-3)
                    if v_rsoa_column:
                        # Voltage unit: V
                        current_ops[-1] = np.hstack([current_ops[-1], float(line_data[v_rsoa_column])])
    return np.asarray(expected_wl), np.asarray(current_ops)


if __name__ == "__main__":
    file_path = "C:\\Users\\<USER>\\Desktop\\operating_points.txt"
    fop, minwl, maxwl = process_ops(file_path)
    print("Failed points: {}, minwl = {}, maxwl = {}".format(fop, minwl, maxwl))
    # move_files(keywords=fop, dest_dir='C:\\Users\\<USER>\\Desktop\\ECL_tuning_fine_mesh_failed\\measurements\\',
    #            output_dir='C:\\Users\\<USER>\\Desktop\\ECL_tuning_fine_mesh_failed\\failed_points\\')
