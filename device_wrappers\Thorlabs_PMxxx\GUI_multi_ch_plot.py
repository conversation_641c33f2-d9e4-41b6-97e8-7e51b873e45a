# Install anyvisa: python -m pip install anyvisa*.whl
import tkinter as tk
from tkinter import ttk, messagebox
from threading import Thread
import queue
import anyvisa
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.animation import FuncAnimation
import argparse
import sys


class DynamicPowerPlotApp(tk.Tk):
    def __init__(self, device_addresses=None, wavelength=1550):
        super().__init__()
        self.wavelength = wavelength  # nm
        self.device_addresses = device_addresses
        self.num_channels = 0
        self.title("Multi-Channel Power Meter with Live Plot")
        self.geometry("800x600")  # Initial size, will be adjusted after device detection
        self.running = False
        self.measurement_queue = queue.Queue()
        self.instruments = []
        self.max_points = 100  # Number of points to display
        self.update_interval = 100  # ms

        # Initialize data buffers - will be populated dynamically
        self.times = np.linspace(-10, 0, self.max_points)  # 10-second window
        self.channel_data = []  # List of data arrays for each channel
        self.channel_vars = []  # List of StringVar for each channel display
        self.plot_lines = []    # List of plot lines for each channel

        # Initialize devices
        try:
            self._initialize_devices()
            if self.num_channels == 0:
                raise ConnectionError("No Thorlabs PMxxx devices found")

            # Initialize dynamic data structures
            self._initialize_data_structures()

        except Exception as e:
            messagebox.showerror("Error", f"Initialization failed: {str(e)}")
            self.destroy()
            return

        # Adjust window size based on number of channels
        self._adjust_window_size()

        # Create GUI layout
        self._create_gui_layout()

    def _initialize_devices(self):
        """Initialize devices based on provided addresses or auto-detection"""
        if self.device_addresses:
            self._initialize_manual_devices()
        else:
            self._initialize_auto_detected_devices()

    def _initialize_manual_devices(self):
        """Initialize devices from manually specified addresses"""
        print(f"Attempting to connect to {len(self.device_addresses)} specified devices...")

        for i, address in enumerate(self.device_addresses):
            try:
                # Find all available devices first
                devices_list = anyvisa.AnyVisa.FindResources("USB?*::INSTR")

                # Look for the specific device address
                device_found = None
                for device in devices_list:
                    if address in str(device) or address == str(device):
                        device_found = device
                        break

                if device_found is None:
                    print(f"Warning: Device {address} not found in available devices")
                    continue

                # Configure the device
                instr = device_found
                instr.open()

                try:
                    identity = instr.query("SYST:SENS:IDN?").strip()
                except:
                    print(f"Warning: Could not verify device {address} identity")
                    instr.close()
                    continue

                self._configure_device(instr)
                self.instruments.append(instr)
                print(f"Successfully connected to device {i+1}: {address}")

            except Exception as e:
                print(f"Failed to connect to device {address}: {str(e)}")

        self.num_channels = len(self.instruments)
        print(f"Successfully initialized {self.num_channels} devices")

    def _initialize_auto_detected_devices(self):
        """Auto-detect and initialize all available Thorlabs PMxxx devices"""
        print("Auto-detecting Thorlabs PMxxx devices...")

        try:
            devices_list = anyvisa.AnyVisa.FindResources("USB?*::INSTR")
            print(f"Found {len(devices_list)} USB devices:")

            for i, device in enumerate(devices_list):
                try:
                    instr = device
                    instr.open()

                    try:
                        identity = instr.query("SYST:SENS:IDN?").strip()
                        print(f"  - Device {i+1} at {device}: {identity}")
                        self._configure_device(instr)
                        self.instruments.append(instr)
                    except:
                        print(f"Could not query device {i+1}, skipping")
                        instr.close()

                except Exception as e:
                    print(f"Failed to connect to device {i+1}: {str(e)}")

            self.num_channels = len(self.instruments)
            print(f"Auto-detected {self.num_channels} Thorlabs PMxxx devices")

        except Exception as e:
            print(f"Device detection failed: {str(e)}")
            self.num_channels = 0

    def _configure_device(self, instr):
        """Configure a single power meter device"""
        instr.write("SENS:RANGE:AUTO ON")
        instr.write('SENS:FREQ:MODE CW')
        instr.write(f"SENS:CORR:WAV {int(self.wavelength)}")
        instr.write("SENS:POW:UNIT dBm")
        instr.write("SENS:AVER:1")
        instr.write("CONF:POW")

    def _initialize_data_structures(self):
        """Initialize dynamic data structures based on number of channels"""
        self.channel_data = []
        self.channel_vars = []

        for _ in range(self.num_channels):
            # Create data buffer for each channel
            self.channel_data.append(np.full(self.max_points, np.nan))
            # Create StringVar for each channel display
            self.channel_vars.append(tk.StringVar(value="---.--- dBm"))

    def _adjust_window_size(self):
        """Adjust window size based on number of channels"""
        if self.num_channels > 0:
            # Calculate optimal width based on number of channels
            width = max(800, min(1400, 200 * self.num_channels + 400))
            self.geometry(f"{width}x600")

            # Update title to show number of channels
            self.title(f"Multi-Channel Power Meter ({self.num_channels} channels)")

    def _create_gui_layout(self):
        """Create the GUI layout dynamically based on number of channels"""
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(pady=10, expand=True, fill='both')

        # Value displays - create dynamically based on number of channels
        self.value_frame = ttk.Frame(self.main_frame)
        self.value_frame.pack(pady=10)

        self._create_channel_displays()

        # Plot setup - create dynamically based on number of channels
        self._create_plot_area()

        # Control button
        self.btn = ttk.Button(self, text="Start", command=self.toggle_measurement)
        self.btn.pack(pady=10)

        # Start update loop
        self.ani = FuncAnimation(self.fig, self.update_plot, interval=50, blit=True)
        self.protocol("WM_DELETE_WINDOW", self.on_close)

    def _create_channel_displays(self):
        """Create value display labels dynamically for each channel"""
        if self.num_channels == 0:
            # Show message when no devices are connected
            ttk.Label(self.value_frame, text="No devices connected",
                     font=('Helvetica', 16), foreground='red').pack(pady=20)
            return

        # Calculate optimal grid layout
        cols = min(self.num_channels, 4)  # Max 4 columns

        for i in range(self.num_channels):
            row = i // cols
            col = i % cols

            # Channel label
            ttk.Label(self.value_frame, text=f"Power Meter {i+1}",
                     font=('Helvetica', 12)).grid(row=row*2, column=col, padx=20, pady=(10,0))

            # Value display with minimum width to prevent overflow
            ttk.Label(self.value_frame, textvariable=self.channel_vars[i],
                     font=('Helvetica', 22), width=12, anchor='center').grid(row=row*2+1, column=col, padx=20, pady=(0,10))

    def _create_plot_area(self):
        """Create the plotting area with dynamic number of lines"""
        self.plot_frame = ttk.Frame(self.main_frame)
        self.plot_frame.pack(pady=10, expand=True, fill='both')

        self.fig, self.ax = plt.subplots(figsize=(8, 4))
        self.plot_lines = []

        if self.num_channels > 0:
            # Create plot lines for each channel with different colors
            colors = plt.cm.tab10(np.linspace(0, 1, self.num_channels))

            for i in range(self.num_channels):
                line, = self.ax.plot(self.times, self.channel_data[i],
                                   label=f'PM{i+1}', color=colors[i])
                self.plot_lines.append(line)

            self.ax.legend()
        else:
            # Show empty plot when no devices
            self.ax.text(0.5, 0.5, 'No devices connected',
                        transform=self.ax.transAxes, ha='center', va='center',
                        fontsize=16, color='red')

        self.ax.set_xlabel('Time (s)')
        self.ax.set_ylabel('Power (dBm)')
        self.ax.set_xlim(-10, 0)
        # self.ax.set_ylim(-60, 10)  # Adjust based on expected power range
        self.ax.set_ylim(10*np.log10(1e-6), 10*np.log10(20))  # The measurement range of S155C

        self.ax.grid(True)

        self.canvas = FigureCanvasTkAgg(self.fig, master=self.plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(expand=True, fill='both')

    def toggle_measurement(self):
        if not self.running:
            self.running = True
            self.btn.config(text="Stop")
            Thread(target=self.measurement_loop, daemon=True).start()
        else:
            self.running = False
            self.btn.config(text="Start")

    def measurement_loop(self):
        while self.running:
            try:
                # Query all instruments and collect measurements
                measurements = []
                for i, instr in enumerate(self.instruments):
                    try:
                        power = float(instr.query("MEAS:POW?").strip())
                        measurements.append(power)
                    except Exception as e:
                        print(f"Error reading from instrument {i+1}: {str(e)}")
                        measurements.append(np.nan)  # Use NaN for failed readings

                if measurements:  # Only put data if we have measurements
                    self.measurement_queue.put(tuple(measurements))

            except Exception as e:
                print(f"Measurement loop error: {str(e)}")
                # Put error data for all channels
                error_data = tuple([np.nan] * self.num_channels)
                self.measurement_queue.put(error_data)
                self.running = False
                self.after(0, lambda: self.btn.config(text="Start"))

    def update_plot(self, frame):
        try:
            while True:
                # Get new data
                measurements = self.measurement_queue.get_nowait()
                if self.running and len(measurements) == self.num_channels:
                    # Update data for each channel
                    for i in range(self.num_channels):
                        # Shift data left
                        self.channel_data[i] = np.roll(self.channel_data[i], -1)
                        # Update buffer with new measurement
                        self.channel_data[i][-1] = measurements[i]

                        # Update display value
                        if np.isnan(measurements[i]):
                            self.channel_vars[i].set("Error")
                        else:
                            self.channel_vars[i].set(self._format_power_value(measurements[i]))

        except queue.Empty:
            pass

        # Update plot data for all channels
        for i in range(self.num_channels):
            self.plot_lines[i].set_ydata(self.channel_data[i])

        return self.plot_lines

    def _format_power_value(self, power_dbm):
        """
        Format power value for display, handling overflow and extreme values.

        Args:
            power_dbm (float): Power value in dBm

        Returns:
            str: Formatted power string for display
        """
        try:
            # Handle NaN values
            if np.isnan(power_dbm):
                return "Error"

            # Handle infinite values
            if np.isinf(power_dbm):
                return "±∞ dBm" if power_dbm > 0 else "-∞ dBm"

            # Handle very large values (overflow)
            if power_dbm > 999.99 or power_dbm < -999.99:
                return "-999 dBm"

            # Handle normal range with appropriate precision
            if abs(power_dbm) >= 100:
                # For values >= 100 or <= -100, use 1 decimal place to fit better
                return f"{power_dbm:.1f} dBm"
            elif abs(power_dbm) >= 10:
                # For values >= 10 or <= -10, use 2 decimal places
                return f"{power_dbm:.2f} dBm"
            else:
                # For small values, use 3 decimal places for better precision
                return f"{power_dbm:.3f} dBm"

        except (ValueError, TypeError):
            return "Error"

    def on_close(self):
        self.running = False
        for instr in self.instruments:
            instr.close()
        self.destroy()
        plt.close('all')


def parse_arguments():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(
        description="Multi-Channel Thorlabs PMxxx Power Meter GUI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Auto-detect all available devices
  python GUI_2ch_plot.py

  # Specify single device
  python GUI_2ch_plot.py --devices "USB0::0x1313::0x8078::P0012345::INSTR"

  # Specify multiple devices
  python GUI_2ch_plot.py --devices "USB0::0x1313::0x8078::P0012345::INSTR" "USB0::0x1313::0x8078::P0012346::INSTR"

  # Custom wavelength
  python GUI_2ch_plot.py --wavelength 1310
        """
    )

    parser.add_argument(
        '--devices', '-d',
        nargs='*',
        help='Specify device addresses manually (optional). If not provided, auto-detection will be used.'
    )

    parser.add_argument(
        '--wavelength', '-w',
        type=int,
        default=1550,
        help='Wavelength in nm for calibration (default: 1550)'
    )

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_arguments()

    print("Multi-Channel Thorlabs PMxxx Power Meter GUI")
    print("=" * 50)

    if args.devices:
        print(f"Manual mode: Using {len(args.devices)} specified device(s)")
        for i, device in enumerate(args.devices):
            print(f"  Device {i+1}: {device}")
    else:
        print("Auto-detection mode: Searching for all available Thorlabs PMxxx devices")

    print(f"Wavelength: {args.wavelength} nm")
    print()

    try:
        app = DynamicPowerPlotApp(device_addresses=args.devices, wavelength=args.wavelength)
        app.mainloop()
    except Exception as e:
        print(f"Application failed to start: {str(e)}")
        sys.exit(1)