# TODO:
# 1. OB2B calibration without EDWA chip:
# 	a. record the spectrum for each wavelength and its attenuated version
# 2. Turn on the pumps
# 	a. record the spectrum for each wavelength and its attenuated version
 
#  sweep wl and input power 
from __future__ import division
import numpy as np
import matplotlib.pyplot as plt
import traceback
import time
import os.path

from device_wrappers.Newfocus_TLB6700 import NewFocus_TLB6700_wrapper
from device_wrappers.Thorlabs_VOA_DV1550AA.DV1550AA_wrapper import VOAController
from device_wrappers.Ando_AQ6317B_OSA.AQ6317B_wrapper import ANDO_AQ6317B
from utils import dBm_to_mW, mW_to_dBm

OSA_sens_lvl = ['NORM RANGE HOLD', 'NORM RANGE AUTO', 'MID', 'HIGH1', 'HIGH2', 'HIGH3']

def find_peak(x, y):
    max_idx = np.argmax(y)
    max_x = x[max_idx]
    max_y = y[max_idx]
    return max_x, max_y

class GainCharacterization:
    def __init__(self, save_dir, wl_start=1530, wl_end=1570, wl_step=1):
        self.ecl = None
        assert (wl_start >= 1520) and (wl_end <= 1570), 'Wavelength range is out of the ECL range (1520 - 1570 nm).'
        self.start_wavelength = wl_start
        self.end_wavelength = wl_end
        self.wavelength_step = wl_step

        self.osa = None
        self.oas_trace = 'A'
        self.OSA_config = {'start_wl': 1528, 'end_wl': 1572, 'resolution': 0.01, 'pts': 10001, 'sens': 'HIGH2'}

        self.wl_offset_OSA2ECL = 0.2

        self.voa = None     # from -30 ~ 8 dBm, 12 points.
        self.power_step = 5 # dB
        self.voa_calibrated_wavelength = None

        self.save_dir = save_dir

    @property
    def sweep_wls(self):
        return np.arange(self.start_wavelength, self.end_wavelength + self.wavelength_step, self.wavelength_step)

    def connect_to_devices(self):
        self.ecl = NewFocus_TLB6700_wrapper.NewFocus6700(id=4106, key='6700 SN70006')
        self.ecl.connect_laser(True)
        self.voa = VOAController(port='COM9')
        self.osa = ANDO_AQ6317B(u'GPIB0::16::INSTR')

    def disconnect_from_devices(self):
        self.ecl.disconnect_laser()
        self.voa.close()
        self.osa.clean_up()

    def config_OSA(self):
        # self.osa.change_x_scale_unit('wl')
        # self.osa.set_power_unit('dBm')
        if self.OSA_config.get('start_wl', None) and self.OSA_config.get('end_wl', None):
            self.osa.set_wl_range(wl_start=self.OSA_config['start_wl'], wl_stop=self.OSA_config['end_wl'])
        if self.OSA_config.get('span', None) and self.OSA_config.get('center_wl', None):
            self.osa.set_wl_span(wl_center=self.OSA_config['center_wl'], wl_span=self.OSA_config['span'])
        if self.OSA_config.get('resolution', None):
            self.osa.set_wl_resolution(wl_resln=self.OSA_config['resolution'])
        if self.OSA_config.get('pts', None):
            self.osa.set_sampling_point(nb_points=self.OSA_config['pts'])
        if self.OSA_config.get('sens', None):
            self.osa.set_sensitivity(level=self.OSA_config['sens'])

    def set_OSA_range(self, wl_center, span):
        self.osa.set_wl_span(wl_center=wl_center, wl_span=span)

    def init_devices(self):
        self.ecl.set_wl_scan_speed(wl_scan_speed=20.0)  # influence the wl changing speed, 20 nm/s is the max.
        self.ecl.set_wl_scan_limit(wl_scan_limit_value=[1520, 1570])    # full range: [1520, 1570]
        # self.ecl.reset()
        # time.sleep(20)    # reset takes sometime

        print('[INFO] Setting VOA to manual mode and maximum attenuation...')
        self.voa.set_operating_mode('2') # 0: Calibrated Attenuation, 2: manual
        self.voa.set_voltage(voltage_mV=0)  # 0 mV = Maximum attenuation, 5500 mV = Near minimum attenuation
        # cali_wl = self.voa.get_calibrated_wavelength()
        # self.voa.set_system_wavelength(wavelength_nm=1550)
        
        print('[INFO] Reset OSA...')
        # self.osa.initialize_hardware()
        # time.sleep(10)    # TODO: check if this is needed
        # self.osa.header_on(False)
        self.config_OSA()

    def get_ecl_wavelength_range(self):
        return self.ecl.query_wl_scan_limit()
    
    def save_osa_spectrum(self, filename):
        self.osa.single_sweep()
        time.sleep(20)
        wl, pw = self.osa._get_spectrum(channel=self.oas_trace, r_start=1, r_end=self.OSA_config['pts'])
        
        # debug
        wl_max, pw_max = find_peak(wl, pw)
        print(f"Peak wavelength: {wl_max:.2f} nm, power: {pw_max:.2f} dBm ({dBm_to_mW(pw_max):.2f} mW)")
        
        file_path = os.path.join(self.save_dir, filename + '.npy')
        np.save(file_path, np.array([wl, pw]))

    def plot_osa_spetrum(self, filename):
        file_path = os.path.join(self.save_dir, filename + '.npy')
        wl, pw = np.load(file_path)
        plt.plot(wl, pw)
        plt.xlabel('Wavelength (nm)')
        plt.ylabel('Power (dBm)')
        # plt.ylim(-80, 10)
        plt.title(filename)
        plt.show()

    def sweep(self, flag):
        assert flag in ['OB2B', 'EDWA']

        self.connect_to_devices()
        self.init_devices()
        # print(self.voa.get_monotonic_voltage_range())
        self.ecl.set_out_power(power_mW=6)
        self.ecl.set_output_state(value=True)
        time.sleep(5)  # wait for the laser to reach the set power
        
        for wl in self.sweep_wls:
            self.ecl.set_lbd(Lambda=wl + self.wl_offset_OSA2ECL)
            self.ecl.wait_for_tracking()
            voa_voltage = 5500
            self.voa.set_voltage(voltage_mV=voa_voltage)   # Monotonic voltage range: [2810, 5030] mV
            time.sleep(5)

            spectrum_name = f'wl{wl:.2f}nm_voa{voa_voltage}mV_{flag}'
            self.save_osa_spectrum(filename=spectrum_name)
            self.plot_osa_spetrum(filename=spectrum_name)

    
    def temp(self, flag):
        # TODO: seems that the OSA does not have enough resolution to measure the peak. (change input power the peak height does not change.)
        assert flag in ['OB2B', 'EDWA']

        self.connect_to_devices()
        # self.init_devices()
        # print(self.voa.get_monotonic_voltage_range())
        self.ecl.set_out_power(power_mW=15)
        self.ecl.set_output_state(value=True)
        time.sleep(5)  # wait for the laser to reach the set power
        
        wl = 1550
        voa_voltage = 5500
        self.ecl.set_lbd(Lambda=1550)
        self.ecl.wait_for_tracking()
        self.voa.set_voltage(voltage_mV=voa_voltage)   # Monotonic voltage range: [2810, 5030] mV
        time.sleep(5)

        spectrum_name = f'wl{wl:.2f}nm_voa{voa_voltage}mV_{flag}'
        self.set_OSA_range(wl_center=wl, span=3)
        self.save_osa_spectrum(filename=spectrum_name)
        self.plot_osa_spetrum(filename=spectrum_name)


if __name__ == '__main__':
    save_dir = r'C:\Users\<USER>\Documents\PythonProject\temp'
    gain_char = GainCharacterization(save_dir=save_dir)
    try:
        gain_char.temp(flag='OB2B')
    except:
        print(traceback.format_exc())
    finally:
        gain_char.ecl.set_output_state(value=False)
        gain_char.disconnect_from_devices()