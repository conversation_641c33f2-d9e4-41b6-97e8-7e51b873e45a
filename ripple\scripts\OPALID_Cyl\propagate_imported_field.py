from ripple.field import Trans<PERSON><PERSON><PERSON>
from ripple.monitors import FieldMonitor
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.sim_helpers import run_sim
from ripple.field_distribution import ImportedCSTFieldDistribution

from ripple.utils.export_data import plot_field_grid_far_field
import matplotlib.pyplot as plt

# TODO:
#  1) propagate in the resist for 5 um, check mfd and far field

# Imported source from CST
source_field = ImportedCSTFieldDistribution(cst_export_file_path=r'./input/e-field (f=193.75) [1]_subVolume.txt',
                                             cut_axis='z', rough_cut_pos=20.05, skiprows=2, Nix=None, Niy=None)
source_field = TransverseField(distribution=source_field, z0=0, refractive_index=1.53, wavelength=1.55)

monitor_yz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_beam_radius_per_z=True)

scene = CouplingScene(input_field=source_field, target_field=None,
                      background_material_index=1.53,
                      optical_structures=[],
                      sim_size_xy=[40, 40], sim_z_end=5,
                      wavelength_sampling_xy=8,
                      material_wavelength_sampling_z=8,
                      background_wavelength_sampling_z=4,
                      monitors=[monitor_yz],
                      boundary_condition='ABC', boundary_parameters=None)

print(f"Source field resolution before re-meshing [um]: {source_field.res_x, source_field.res_y}")
source_field.view()
plt.show()

run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=False,
        name='optimized', sim_result_dir=None, opt_log_dir=None, show_plot=False)

plot_field_grid_far_field(scene.monitors[-1], folder_path='./data', prefix='5um', save_figure=True)
